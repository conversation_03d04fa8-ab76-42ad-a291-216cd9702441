<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Graphique Présences par Jour</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug - Graphique "Présences par jour (cette semaine)"</h1>
        <p>Diagnostic du problème avec le graphique des présences hebdomadaires.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Frontend:</strong> http://localhost:5174/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Tests API Backend</h3>
            <button onclick="testStatisticsAPI()">📊 Test API Statistiques</button>
            <button onclick="testPresencesByDate()">📅 Test Présences par Date</button>
            <button onclick="testTodaySummary()">📈 Test Résumé Aujourd'hui</button>
        </div>

        <div class="test-section">
            <h3>🔧 Tests Formatage Données</h3>
            <button onclick="testDataFormatting()">🔄 Test Formatage Dashboard</button>
            <button onclick="simulateWeeklyData()">📊 Simuler Données Semaine</button>
            <button onclick="testChartData()">📈 Test Données Graphique</button>
        </div>

        <div class="test-section">
            <h3>🌐 Tests Frontend</h3>
            <button onclick="openDashboard()">🌐 Ouvrir Dashboard</button>
            <button onclick="checkConsoleErrors()">🐛 Vérifier Erreurs Console</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Test de l'API statistiques
        async function testStatisticsAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de l\'API statistiques...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Calculer les dates de la semaine
                const today = new Date();
                const oneWeekAgo = new Date();
                oneWeekAgo.setDate(today.getDate() - 7);

                const startDate = oneWeekAgo.toISOString().split('T')[0];
                const endDate = today.toISOString().split('T')[0];

                console.log(`Dates: ${startDate} à ${endDate}`);

                // Test de l'API présences par jour
                const url = `http://127.0.0.1:8000/api/statistiques/presences/jour/?start_date=${startDate}&end_date=${endDate}`;
                console.log(`URL: ${url}`);

                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                let html = `<div class="success"><h3>📊 Test API Statistiques</h3></div>`;
                html += `<p><strong>URL testée:</strong> ${url}</p>`;
                html += `<p><strong>Status:</strong> ${response.status} ${response.statusText}</p>`;

                if (response.ok) {
                    const data = await response.json();
                    html += `<p><strong>Données reçues:</strong> ${data.length} entrées</p>`;
                    
                    if (data.length > 0) {
                        html += '<h4>🔍 Aperçu des données :</h4>';
                        html += `<pre>${JSON.stringify(data.slice(0, 3), null, 2)}</pre>`;
                        
                        html += `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Jour</th>
                                        <th>Nombre</th>
                                        <th>Total Étudiants</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        data.forEach(item => {
                            html += `
                                <tr>
                                    <td>${item.day || item.date || 'N/A'}</td>
                                    <td>${new Date(item.day || item.date).toLocaleDateString('fr-FR', { weekday: 'long' })}</td>
                                    <td>${item.count || 0}</td>
                                    <td>${item.total_students || 'N/A'}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                    } else {
                        html += '<div class="warning"><p>⚠️ Aucune donnée retournée par l\'API</p></div>';
                    }
                } else {
                    const errorData = await response.text();
                    html += `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test des présences par date
        async function testPresencesByDate() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test des présences par date...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Test de l'API présences standard
                const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    // Grouper par date
                    const groupedByDate = {};
                    presences.forEach(presence => {
                        const date = presence.date;
                        if (!groupedByDate[date]) {
                            groupedByDate[date] = [];
                        }
                        groupedByDate[date].push(presence);
                    });

                    let html = `<div class="success"><h3>📅 Présences par Date</h3></div>`;
                    html += `<p><strong>Total présences:</strong> ${presences.length}</p>`;
                    html += `<p><strong>Dates uniques:</strong> ${Object.keys(groupedByDate).length}</p>`;
                    
                    html += `
                        <table>
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Jour</th>
                                    <th>Nombre de présences</th>
                                    <th>Avec arrivée</th>
                                    <th>Avec départ</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    Object.keys(groupedByDate).sort().reverse().slice(0, 7).forEach(date => {
                        const dayPresences = groupedByDate[date];
                        const withArrival = dayPresences.filter(p => p.heure_arrivee).length;
                        const withDeparture = dayPresences.filter(p => p.heure_depart).length;
                        
                        html += `
                            <tr>
                                <td>${date}</td>
                                <td>${new Date(date).toLocaleDateString('fr-FR', { weekday: 'long' })}</td>
                                <td>${dayPresences.length}</td>
                                <td>${withArrival}</td>
                                <td>${withDeparture}</td>
                            </tr>
                        `;
                    });
                    
                    html += '</tbody></table>';
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test du résumé d'aujourd'hui
        async function testTodaySummary() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test du résumé d\'aujourd\'hui...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/statistiques/presences/aujourd-hui/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const summary = await response.json();
                    
                    let html = `<div class="success"><h3>📈 Résumé d'Aujourd'hui</h3></div>`;
                    html += `<pre>${JSON.stringify(summary, null, 2)}</pre>`;
                    
                    html += `
                        <table>
                            <thead>
                                <tr>
                                    <th>Métrique</th>
                                    <th>Valeur</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Total étudiants</td>
                                    <td>${summary.total_students || 0}</td>
                                </tr>
                                <tr>
                                    <td>Présents aujourd'hui</td>
                                    <td>${summary.present_students || 0}</td>
                                </tr>
                                <tr>
                                    <td>Absents aujourd'hui</td>
                                    <td>${summary.absent_students || 0}</td>
                                </tr>
                                <tr>
                                    <td>Taux de présence</td>
                                    <td>${summary.attendance_rate || 0}%</td>
                                </tr>
                            </tbody>
                        </table>
                    `;
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test du formatage des données
        async function testDataFormatting() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test du formatage des données...</p></div>';

            try {
                // Simuler des données comme celles reçues de l'API
                const mockWeeklyData = [
                    { day: '2025-01-27', count: 15, total_students: 20 },
                    { day: '2025-01-28', count: 18, total_students: 20 },
                    { day: '2025-01-29', count: 12, total_students: 20 },
                    { day: '2025-01-30', count: 16, total_students: 20 },
                    { day: '2025-01-31', count: 14, total_students: 20 }
                ];

                // Simuler la fonction formatAttendanceByDay
                const formatAttendanceByDay = (weeklyPresences) => {
                    const days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'];
                    const result = [];

                    // Initialiser
                    for (const day of days) {
                        result.push({ day, present: 0, absent: 0 });
                    }

                    let totalStudents = 0;
                    const entryWithTotal = weeklyPresences.find(p => p.total_students !== undefined);
                    if (entryWithTotal) {
                        totalStudents = entryWithTotal.total_students;
                    }

                    // Parcourir les présences
                    for (const presence of weeklyPresences) {
                        if (!presence.day) continue;

                        const date = new Date(presence.day);
                        const dayOfWeek = date.getDay();

                        if (dayOfWeek === 0 || dayOfWeek === 6) continue;

                        const dayName = days[dayOfWeek - 1];
                        const dayEntry = result.find(d => d.day === dayName);
                        
                        if (dayEntry) {
                            dayEntry.present = presence.count || 0;
                            if (totalStudents > 0) {
                                dayEntry.absent = Math.max(0, totalStudents - dayEntry.present);
                            }
                        }
                    }

                    return result;
                };

                const formattedData = formatAttendanceByDay(mockWeeklyData);

                let html = `<div class="success"><h3>🔄 Test Formatage des Données</h3></div>`;
                
                html += '<h4>📊 Données d\'entrée (simulées) :</h4>';
                html += `<pre>${JSON.stringify(mockWeeklyData, null, 2)}</pre>`;
                
                html += '<h4>📈 Données formatées pour le graphique :</h4>';
                html += `<pre>${JSON.stringify(formattedData, null, 2)}</pre>`;
                
                html += `
                    <table>
                        <thead>
                            <tr>
                                <th>Jour</th>
                                <th>Présents</th>
                                <th>Absents</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                formattedData.forEach(day => {
                    html += `
                        <tr>
                            <td>${day.day}</td>
                            <td>${day.present}</td>
                            <td>${day.absent}</td>
                            <td>${day.present + day.absent}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table>';
                
                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Simuler des données de semaine
        async function simulateWeeklyData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Simulation de données hebdomadaires...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Créer des présences pour cette semaine
                const today = new Date();
                const promises = [];

                for (let i = 0; i < 5; i++) {
                    const date = new Date();
                    date.setDate(today.getDate() - i);
                    
                    // Ignorer les weekends
                    if (date.getDay() === 0 || date.getDay() === 6) continue;
                    
                    const dateStr = date.toISOString().split('T')[0];
                    
                    // Créer quelques présences pour cette date
                    const studentsResponse = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                    
                    if (studentsResponse.ok) {
                        const students = await studentsResponse.json();
                        
                        for (let j = 0; j < Math.min(3, students.length); j++) {
                            const student = students[j];
                            
                            const presenceData = {
                                etudiant: student.id,
                                date: dateStr,
                                heure_arrivee: '08:30:00',
                                statut: 'present'
                            };

                            promises.push(
                                fetch('http://127.0.0.1:8000/api/presences/', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    },
                                    body: JSON.stringify(presenceData)
                                }).catch(err => console.log('Présence déjà existante:', err))
                            );
                        }
                    }
                }

                await Promise.all(promises);

                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Données hebdomadaires simulées</h3>
                        <p>Des présences ont été créées pour cette semaine.</p>
                        <p>Vous pouvez maintenant tester l'API statistiques.</p>
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test des données du graphique
        async function testChartData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test complet des données du graphique...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Simuler exactement ce que fait le dashboard
                const today = new Date();
                const oneWeekAgo = new Date();
                oneWeekAgo.setDate(today.getDate() - 7);

                const startDate = oneWeekAgo.toISOString().split('T')[0];
                const endDate = today.toISOString().split('T')[0];

                // 1. Test API statistiques
                const statsResponse = await fetch(`http://127.0.0.1:8000/api/statistiques/presences/jour/?start_date=${startDate}&end_date=${endDate}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                let html = `<div class="success"><h3>📈 Test Complet Données Graphique</h3></div>`;
                html += `<p><strong>Période:</strong> ${startDate} à ${endDate}</p>`;

                if (statsResponse.ok) {
                    const weeklyPresences = await statsResponse.json();
                    html += `<p><strong>Données API:</strong> ${weeklyPresences.length} entrées</p>`;

                    // 2. Test formatage
                    const formatAttendanceByDay = (weeklyPresences) => {
                        const days = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'];
                        const result = [];

                        for (const day of days) {
                            result.push({ day, present: 0, absent: 0 });
                        }

                        let totalStudents = 0;
                        const entryWithTotal = weeklyPresences.find(p => p.total_students !== undefined);
                        if (entryWithTotal) {
                            totalStudents = entryWithTotal.total_students;
                        }

                        for (const presence of weeklyPresences) {
                            if (!presence.day) continue;

                            const date = new Date(presence.day);
                            const dayOfWeek = date.getDay();

                            if (dayOfWeek === 0 || dayOfWeek === 6) continue;

                            const dayName = days[dayOfWeek - 1];
                            const dayEntry = result.find(d => d.day === dayName);
                            
                            if (dayEntry) {
                                dayEntry.present = presence.count || 0;
                                if (totalStudents > 0) {
                                    dayEntry.absent = Math.max(0, totalStudents - dayEntry.present);
                                }
                            }
                        }

                        return result;
                    };

                    const attendanceByDay = formatAttendanceByDay(weeklyPresences);
                    
                    html += '<h4>📊 Données brutes de l\'API :</h4>';
                    html += `<pre>${JSON.stringify(weeklyPresences, null, 2)}</pre>`;
                    
                    html += '<h4>📈 Données formatées pour le graphique :</h4>';
                    html += `<pre>${JSON.stringify(attendanceByDay, null, 2)}</pre>`;
                    
                    // Vérifier si les données sont valides pour le graphique
                    const hasData = attendanceByDay.some(day => day.present > 0 || day.absent > 0);
                    
                    if (hasData) {
                        html += '<div class="success"><p>✅ Les données sont valides pour le graphique</p></div>';
                    } else {
                        html += '<div class="warning"><p>⚠️ Aucune donnée valide pour le graphique</p></div>';
                    }
                    
                } else {
                    const errorData = await statsResponse.text();
                    html += `<div class="error"><p>❌ Erreur API: ${errorData}</p></div>`;
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Ouvrir le dashboard
        function openDashboard() {
            window.open('http://localhost:5174/dashboard', '_blank');
        }

        // Vérifier les erreurs console
        function checkConsoleErrors() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="info">
                    <h3>🐛 Vérification des Erreurs Console</h3>
                    <p>Ouvrez les outils de développement (F12) et consultez l'onglet Console pour voir les erreurs.</p>
                    <p>Recherchez particulièrement :</p>
                    <ul>
                        <li>Erreurs de réseau (Failed to fetch)</li>
                        <li>Erreurs 404 ou 500</li>
                        <li>Erreurs de parsing JSON</li>
                        <li>Erreurs de composants React</li>
                    </ul>
                    <p><strong>Conseil :</strong> Rechargez le dashboard et observez les requêtes dans l'onglet Network.</p>
                </div>
            `;
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Backend actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Backend répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Backend inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Initialisation
        checkServerStatus();
    </script>
</body>
</html>

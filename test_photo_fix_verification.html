<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Correction Photo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .fix-highlight {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .photo-preview {
            max-width: 100px;
            max-height: 100px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vérification Correction Photo</h1>
        <p>Test de la correction du problème de sauvegarde des photos dans la base de données.</p>
        
        <div class="success">
            <h3>✅ Corrections appliquées</h3>
            <ul>
                <li><strong>Problème parent :</strong> Mapping des relations corrigé (père/mère avec accents)</li>
                <li><strong>Problème photo :</strong> Indentation corrigée dans register_face</li>
                <li><strong>Fonctionnalité "Voir" :</strong> Ajoutée pour visualiser les détails</li>
            </ul>
        </div>

        <div class="fix-highlight">
            <h3>🔧 Correction technique photo</h3>
            <p><strong>Problème :</strong> L'indentation incorrecte faisait que la sauvegarde de la photo n'était exécutée que si la reconnaissance faciale réussissait.</p>
            <p><strong>Solution :</strong> Correction de l'indentation pour que la photo soit toujours sauvegardée dans la base de données.</p>
            <pre>if result['success']:
    # Traitement de l'image
    image_data = base64.b64decode(base64_image)
    image_name = f"{etudiant.id}_face_{timestamp}.jpg"
    
    # Sauvegarde dans la base de données
    etudiant.photo.save(image_name, ContentFile(image_data), save=True)</pre>
        </div>

        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-step">
            <h3>🧪 Tests à effectuer</h3>
            <ol>
                <li><strong>Test création étudiant complet :</strong>
                    <button onclick="testCompleteStudentCreation()">🧪 Tester</button>
                </li>
                <li><strong>Vérifier photos existantes :</strong>
                    <button onclick="checkExistingPhotos()">📸 Vérifier</button>
                </li>
                <li><strong>Test upload photo seule :</strong>
                    <button onclick="testPhotoUpload()">📷 Tester</button>
                </li>
            </ol>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Test de création complète d'un étudiant avec photo et parent
        async function testCompleteStudentCreation() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de création complète...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // 1. Créer un étudiant
                const studentData = {
                    nom: 'TestPhoto',
                    prenom: 'Etudiant',
                    date_naissance: '2010-01-01',
                    sexe: 'M',
                    statut: 'actif',
                    classe: 1
                };

                resultDiv.innerHTML += '<p>👤 Création de l\'étudiant...</p>';
                const studentResponse = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(studentData)
                });

                if (!studentResponse.ok) {
                    throw new Error('Échec de la création de l\'étudiant');
                }

                const createdStudent = await studentResponse.json();
                resultDiv.innerHTML += `<div class="success"><p>✅ Étudiant créé avec ID: ${createdStudent.id}</p></div>`;

                // 2. Créer le parent avec mapping correct
                const parentData = {
                    nom: 'TestParent',
                    prenom: 'Parent',
                    telephone: '+33123456789',
                    email: '<EMAIL>',
                    relation: 'père', // Avec accent
                    notifications_sms: true,
                    notifications_email: false,
                    etudiant: createdStudent.id
                };

                resultDiv.innerHTML += '<p>👨‍👩‍👧‍👦 Création du parent...</p>';
                const parentResponse = await fetch('http://127.0.0.1:8000/api/parents/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(parentData)
                });

                if (parentResponse.ok) {
                    const createdParent = await parentResponse.json();
                    resultDiv.innerHTML += `<div class="success"><p>✅ Parent créé avec ID: ${createdParent.id}</p></div>`;
                } else {
                    const errorData = await parentResponse.text();
                    resultDiv.innerHTML += `<div class="error"><p>❌ Échec parent: ${errorData}</p></div>`;
                }

                // 3. Ajouter une photo
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
                
                resultDiv.innerHTML += '<p>📸 Ajout de la photo...</p>';
                const photoResponse = await fetch(`http://127.0.0.1:8000/api/etudiants/${createdStudent.id}/register_face/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64
                    })
                });

                if (photoResponse.ok) {
                    const photoResult = await photoResponse.json();
                    resultDiv.innerHTML += `<div class="success"><p>✅ Photo uploadée: ${JSON.stringify(photoResult)}</p></div>`;
                } else {
                    const errorData = await photoResponse.text();
                    resultDiv.innerHTML += `<div class="error"><p>❌ Échec photo: ${errorData}</p></div>`;
                }

                // 4. Vérifier que la photo est dans la base de données
                resultDiv.innerHTML += '<p>🔍 Vérification de la photo en base...</p>';
                const checkResponse = await fetch(`http://127.0.0.1:8000/api/etudiants/${createdStudent.id}/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (checkResponse.ok) {
                    const studentWithPhoto = await checkResponse.json();
                    if (studentWithPhoto.photo) {
                        resultDiv.innerHTML += `
                            <div class="success">
                                <h3>🎉 Test complet réussi !</h3>
                                <p><strong>Étudiant:</strong> ${studentWithPhoto.nom} ${studentWithPhoto.prenom}</p>
                                <p><strong>Photo URL:</strong> <code>${studentWithPhoto.photo}</code></p>
                                <img src="${studentWithPhoto.photo}" alt="Photo" class="photo-preview" 
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div style="display:none; color:red;">❌ Erreur de chargement de l'image</div>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML += `<div class="error"><p>❌ Photo non trouvée en base de données</p></div>`;
                    }
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les photos existantes
        async function checkExistingPhotos() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des photos existantes...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

                const students = await response.json();
                
                let html = `<div class="info"><h3>📸 Photos des étudiants (${students.length} étudiants)</h3></div>`;
                
                students.forEach((student, index) => {
                    const hasPhoto = student.photo && student.photo !== '';
                    const photoStatus = hasPhoto ? '✅ Photo présente' : '❌ Pas de photo';
                    const cardClass = hasPhoto ? 'success' : 'warning';
                    
                    html += `
                        <div class="${cardClass}" style="margin: 10px 0;">
                            <h4>${student.prenom} ${student.nom} (ID: ${student.id})</h4>
                            <p><strong>Status:</strong> ${photoStatus}</p>
                            ${hasPhoto ? `
                                <p><strong>URL:</strong> <code>${student.photo}</code></p>
                                <img src="${student.photo}" alt="Photo" class="photo-preview" 
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                <div style="display:none; color:red;">❌ Erreur de chargement</div>
                            ` : '<p>💡 Aucune photo enregistrée</p>'}
                        </div>
                    `;
                });

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test d'upload de photo seule
        async function testPhotoUpload() {
            const resultDiv = document.getElementById('result');
            
            const studentId = prompt('ID de l\'étudiant pour tester l\'upload photo:');
            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Test annulé</p></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info"><p>⏳ Test d\'upload photo...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Image de test plus grande (carré rouge 10x10)
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/register_face/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    // Vérifier immédiatement si la photo est en base
                    const checkResponse = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/`, {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });

                    if (checkResponse.ok) {
                        const student = await checkResponse.json();
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>✅ Upload photo réussi !</h3>
                                <p><strong>Étudiant:</strong> ${student.nom} ${student.prenom}</p>
                                <p><strong>Réponse API:</strong> ${JSON.stringify(result)}</p>
                                <p><strong>Photo en base:</strong> ${student.photo ? '✅ Oui' : '❌ Non'}</p>
                                ${student.photo ? `
                                    <p><strong>URL:</strong> <code>${student.photo}</code></p>
                                    <img src="${student.photo}" alt="Photo" class="photo-preview">
                                ` : ''}
                            </div>
                        `;
                    }
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Échec de l'upload</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

import React, { useState, useEffect } from 'react';
import { Switch } from '@headlessui/react';

interface Parent {
  id: number;
  nom: string;
  prenom: string;
  telephone: string;
  email: string;
  relation: string;
  notifications_sms: boolean;
  notifications_email: boolean;
  notifications_arrivee: boolean;
  notifications_depart: boolean;
}

interface NotificationSettingsProps {
  parent: Parent;
  onUpdate: (parentId: number, settings: Partial<Parent>) => void;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ parent, onUpdate }) => {
  const [settings, setSettings] = useState({
    notifications_sms: parent.notifications_sms,
    notifications_email: parent.notifications_email,
    notifications_arrivee: parent.notifications_arrivee,
    notifications_depart: parent.notifications_depart,
  });

  const [isLoading, setIsLoading] = useState(false);

  const handleToggle = async (field: keyof typeof settings) => {
    const newValue = !settings[field];
    const newSettings = { ...settings, [field]: newValue };
    
    setSettings(newSettings);
    setIsLoading(true);

    try {
      await onUpdate(parent.id, { [field]: newValue });
    } catch (error) {
      // Revert on error
      setSettings(settings);
      console.error('Erreur lors de la mise à jour:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Paramètres de notification
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {parent.prenom} {parent.nom} ({parent.relation})
        </div>
      </div>

      <div className="space-y-6">
        {/* Notifications générales */}
        <div className="border-b border-gray-200 dark:border-gray-700 pb-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Types de notifications
          </h4>
          
          <div className="space-y-4">
            {/* SMS */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Notifications SMS
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Recevoir des SMS sur le numéro {parent.telephone}
                </p>
              </div>
              <Switch
                checked={settings.notifications_sms}
                onChange={() => handleToggle('notifications_sms')}
                disabled={isLoading}
                className={`${
                  settings.notifications_sms ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50`}
              >
                <span
                  className={`${
                    settings.notifications_sms ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
            </div>

            {/* Email */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Notifications Email
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {parent.email ? `Recevoir des emails sur ${parent.email}` : 'Aucun email configuré'}
                </p>
              </div>
              <Switch
                checked={settings.notifications_email}
                onChange={() => handleToggle('notifications_email')}
                disabled={isLoading || !parent.email}
                className={`${
                  settings.notifications_email ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50`}
              >
                <span
                  className={`${
                    settings.notifications_email ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
            </div>
          </div>
        </div>

        {/* Événements spécifiques */}
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Événements à notifier
          </h4>
          
          <div className="space-y-4">
            {/* Arrivée */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  🟢 Notifications d'arrivée
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Être notifié quand votre enfant arrive à l'école
                </p>
              </div>
              <Switch
                checked={settings.notifications_arrivee}
                onChange={() => handleToggle('notifications_arrivee')}
                disabled={isLoading}
                className={`${
                  settings.notifications_arrivee ? 'bg-green-600' : 'bg-gray-200 dark:bg-gray-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50`}
              >
                <span
                  className={`${
                    settings.notifications_arrivee ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
            </div>

            {/* Départ */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  🟠 Notifications de départ
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Être notifié quand votre enfant quitte l'école
                </p>
              </div>
              <Switch
                checked={settings.notifications_depart}
                onChange={() => handleToggle('notifications_depart')}
                disabled={isLoading}
                className={`${
                  settings.notifications_depart ? 'bg-orange-600' : 'bg-gray-200 dark:bg-gray-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50`}
              >
                <span
                  className={`${
                    settings.notifications_depart ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
            </div>
          </div>
        </div>

        {/* Résumé des paramètres */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Résumé de vos notifications
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
            {settings.notifications_arrivee && (
              <div className="flex items-center">
                <span className="text-green-500 mr-2">✓</span>
                Arrivée par {settings.notifications_sms ? 'SMS' : ''} {settings.notifications_sms && settings.notifications_email ? 'et' : ''} {settings.notifications_email ? 'Email' : ''}
              </div>
            )}
            {settings.notifications_depart && (
              <div className="flex items-center">
                <span className="text-orange-500 mr-2">✓</span>
                Départ par {settings.notifications_sms ? 'SMS' : ''} {settings.notifications_sms && settings.notifications_email ? 'et' : ''} {settings.notifications_email ? 'Email' : ''}
              </div>
            )}
            {!settings.notifications_arrivee && !settings.notifications_depart && (
              <div className="flex items-center text-gray-500">
                <span className="mr-2">ℹ</span>
                Aucune notification activée
              </div>
            )}
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-50 dark:bg-gray-800 dark:bg-opacity-50 flex items-center justify-center rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}
    </div>
  );
};

export default NotificationSettings;

{"name": "Gestion Présence Intelligente", "short_name": "GPI", "description": "Système de gestion de présence scolaire avec reconnaissance faciale", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "fr", "categories": ["education", "productivity"], "icons": [{"src": "/icons/icon-144x144.svg", "sizes": "144x144", "type": "image/svg+xml", "purpose": "any"}, {"src": "/icons/icon-192x192.svg", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any"}, {"src": "/icons/icon-192x192.svg", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any"}], "shortcuts": [{"name": "Reconnaissance", "short_name": "Scanner", "description": "Scanner un visage pour pointer", "url": "/recognition"}, {"name": "Dashboard", "short_name": "Tableau de bord", "description": "Voir les statistiques", "url": "/dashboard"}, {"name": "Étudiants", "short_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> les étudiants", "url": "/students"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}
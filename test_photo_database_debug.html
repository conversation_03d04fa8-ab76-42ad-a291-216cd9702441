<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Photo Base de Données</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .student-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .photo-preview {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Photo Base de Données</h1>
        <p>Vérification de l'enregistrement des photos dans la base de données.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <button onclick="checkAllStudents()">👥 Vérifier tous les étudiants</button>
        <button onclick="testPhotoUpload()">📸 Tester upload photo</button>
        <button onclick="checkPhotoFiles()">📁 Vérifier fichiers photos</button>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Vérifier tous les étudiants et leurs photos
        async function checkAllStudents() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des étudiants...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Récupérer tous les étudiants
                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

                const students = await response.json();
                
                if (students.length === 0) {
                    resultDiv.innerHTML = '<div class="warning"><p>⚠️ Aucun étudiant trouvé</p></div>';
                    return;
                }

                let html = `<div class="success"><h3>✅ ${students.length} étudiant(s) trouvé(s)</h3></div>`;
                
                students.forEach((student, index) => {
                    const hasPhoto = student.photo && student.photo !== '';
                    const photoStatus = hasPhoto ? '✅ Photo présente' : '❌ Pas de photo';
                    const photoClass = hasPhoto ? 'success' : 'error';
                    
                    html += `
                        <div class="student-card">
                            <h4>${student.prenom} ${student.nom} (ID: ${student.id})</h4>
                            <p><strong>Classe:</strong> ${student.classe_nom || 'Inconnue'}</p>
                            <p><strong>Status photo:</strong> <span class="${photoClass}">${photoStatus}</span></p>
                            ${hasPhoto ? `
                                <p><strong>URL photo:</strong> <code>${student.photo}</code></p>
                                <div>
                                    <img src="${student.photo}" alt="Photo de ${student.prenom}" class="photo-preview" 
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div style="display:none; color:red;">❌ Erreur de chargement de l'image</div>
                                </div>
                            ` : '<p style="color: #856404;">💡 Aucune photo enregistrée dans la base de données</p>'}
                            <button onclick="checkStudentDetails(${student.id})">🔍 Détails</button>
                        </div>
                    `;
                });

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les détails d'un étudiant spécifique
        async function checkStudentDetails(studentId) {
            try {
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

                const student = await response.json();
                
                alert(`Détails de l'étudiant ${student.prenom} ${student.nom}:
                
ID: ${student.id}
Photo: ${student.photo || 'Aucune'}
Données biométriques: ${student.donnees_biometriques || 'Aucune'}
Date de création: ${student.created_at}
Date de modification: ${student.updated_at}`);

            } catch (error) {
                alert(`Erreur: ${error.message}`);
            }
        }

        // Tester l'upload d'une photo
        async function testPhotoUpload() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test d\'upload photo...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Créer une image de test simple (pixel rouge en base64)
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
                
                // Demander l'ID de l'étudiant
                const studentId = prompt('ID de l\'étudiant pour tester l\'upload photo:');
                if (!studentId) {
                    throw new Error('ID étudiant requis');
                }

                // Envoyer la photo
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/register_face/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        update_photo: true
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Upload photo réussi !</h3>
                            <p><strong>Étudiant ID:</strong> ${studentId}</p>
                            <h4>Réponse du serveur:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                            <button onclick="checkAllStudents()">🔄 Recharger la liste</button>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Échec de l'upload</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les fichiers photos sur le serveur
        async function checkPhotoFiles() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des fichiers photos...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Récupérer tous les étudiants
                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

                const students = await response.json();
                
                let html = '<div class="info"><h3>📁 Vérification des fichiers photos</h3></div>';
                
                for (const student of students) {
                    if (student.photo) {
                        // Tester si l'image est accessible
                        try {
                            const imgResponse = await fetch(student.photo);
                            const status = imgResponse.ok ? '✅ Accessible' : `❌ Erreur ${imgResponse.status}`;
                            const size = imgResponse.headers.get('content-length');
                            
                            html += `
                                <div class="student-card">
                                    <h4>${student.prenom} ${student.nom}</h4>
                                    <p><strong>URL:</strong> <code>${student.photo}</code></p>
                                    <p><strong>Status:</strong> ${status}</p>
                                    ${size ? `<p><strong>Taille:</strong> ${size} bytes</p>` : ''}
                                </div>
                            `;
                        } catch (error) {
                            html += `
                                <div class="student-card">
                                    <h4>${student.prenom} ${student.nom}</h4>
                                    <p><strong>URL:</strong> <code>${student.photo}</code></p>
                                    <p><strong>Status:</strong> ❌ Erreur de connexion</p>
                                </div>
                            `;
                        }
                    } else {
                        html += `
                            <div class="student-card">
                                <h4>${student.prenom} ${student.nom}</h4>
                                <p><strong>Status:</strong> ⚠️ Pas de photo dans la base de données</p>
                            </div>
                        `;
                    }
                }

                resultDiv.innerHTML = html;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

import React, { useState } from 'react';
import { usePWA } from '../../hooks/usePWA';
import { XMarkIcon, ArrowDownTrayIcon, BellIcon, WifiIcon } from '@heroicons/react/24/outline';

const PWAInstallPrompt: React.FC = () => {
  const {
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    installApp,
    updateApp,
    requestNotificationPermission,
    showNotification
  } = usePWA();

  const [showInstallPrompt, setShowInstallPrompt] = useState(true);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(true);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      await installApp();
      setShowInstallPrompt(false);
      
      // Demander la permission pour les notifications après installation
      setTimeout(async () => {
        try {
          await requestNotificationPermission();
          await showNotification('Installation réussie !', {
            body: 'L\'application est maintenant installée sur votre appareil.',
            icon: '/icons/icon-192x192.png'
          });
        } catch (error) {
          console.log('Notification non envoyée:', error);
        }
      }, 1000);
    } catch (error) {
      console.error('Erreur lors de l\'installation:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await updateApp();
      setShowUpdatePrompt(false);
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEnableNotifications = async () => {
    try {
      const permission = await requestNotificationPermission();
      if (permission === 'granted') {
        await showNotification('Notifications activées !', {
          body: 'Vous recevrez maintenant les notifications de présence.',
          icon: '/icons/icon-192x192.png'
        });
      }
    } catch (error) {
      console.error('Erreur lors de l\'activation des notifications:', error);
    }
  };

  // Prompt d'installation
  if (isInstallable && showInstallPrompt && !isInstalled) {
    return (
      <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 z-50">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <ArrowDownTrayIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Installer l'application
              </h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              Installez l'application sur votre appareil pour un accès rapide et des notifications push.
            </p>
            <div className="flex space-x-2">
              <button
                onClick={handleInstall}
                disabled={isInstalling}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center"
              >
                {isInstalling ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Installation...
                  </>
                ) : (
                  <>
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Installer
                  </>
                )}
              </button>
              <button
                onClick={() => setShowInstallPrompt(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white text-sm font-medium transition-colors duration-200"
              >
                Plus tard
              </button>
            </div>
          </div>
          <button
            onClick={() => setShowInstallPrompt(false)}
            className="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    );
  }

  // Prompt de mise à jour
  if (isUpdateAvailable && showUpdatePrompt) {
    return (
      <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-blue-50 dark:bg-blue-900 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-4 z-50">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <ArrowDownTrayIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                Mise à jour disponible
              </h3>
            </div>
            <p className="text-sm text-blue-700 dark:text-blue-200 mb-4">
              Une nouvelle version de l'application est disponible avec des améliorations et corrections.
            </p>
            <div className="flex space-x-2">
              <button
                onClick={handleUpdate}
                disabled={isUpdating}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center justify-center"
              >
                {isUpdating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Mise à jour...
                  </>
                ) : (
                  <>
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Mettre à jour
                  </>
                )}
              </button>
              <button
                onClick={() => setShowUpdatePrompt(false)}
                className="px-4 py-2 text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100 text-sm font-medium transition-colors duration-200"
              >
                Plus tard
              </button>
            </div>
          </div>
          <button
            onClick={() => setShowUpdatePrompt(false)}
            className="ml-4 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    );
  }

  // Indicateur de statut en ligne/hors ligne
  if (!isOnline) {
    return (
      <div className="fixed top-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-orange-50 dark:bg-orange-900 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-4 z-50">
        <div className="flex items-center">
          <WifiIcon className="h-6 w-6 text-orange-600 dark:text-orange-400 mr-2" />
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-orange-900 dark:text-orange-100">
              Mode hors ligne
            </h3>
            <p className="text-xs text-orange-700 dark:text-orange-200">
              Certaines fonctionnalités peuvent être limitées
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Prompt pour activer les notifications (si installé mais notifications non activées)
  if (isInstalled && Notification.permission === 'default') {
    return (
      <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-green-50 dark:bg-green-900 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-4 z-50">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-2">
              <BellIcon className="h-6 w-6 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">
                Activer les notifications
              </h3>
            </div>
            <p className="text-sm text-green-700 dark:text-green-200 mb-4">
              Recevez des notifications en temps réel pour les arrivées et départs.
            </p>
            <button
              onClick={handleEnableNotifications}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
            >
              <BellIcon className="h-4 w-4 mr-2" />
              Activer
            </button>
          </div>
          <button
            onClick={() => setShowInstallPrompt(false)}
            className="ml-4 text-green-400 hover:text-green-600 dark:hover:text-green-300"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    );
  }

  return null;
};

export default PWAInstallPrompt;

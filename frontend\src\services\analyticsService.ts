import { api } from './apiService';

export interface AnalyticsData {
  attendanceRate: number;
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  weeklyTrend: number[];
  monthlyStats: {
    labels: string[];
    present: number[];
    absent: number[];
    late: number[];
  };
  classStats: {
    className: string;
    attendanceRate: number;
    totalStudents: number;
  }[];
  timeDistribution: {
    labels: string[];
    data: number[];
  };
  predictions: {
    nextWeekAttendance: number;
    riskStudents: number;
    trend: 'up' | 'down' | 'stable';
  };
}

export interface PredictionData {
  studentId: number;
  studentName: string;
  riskLevel: 'low' | 'medium' | 'high';
  attendanceRate: number;
  predictedAbsences: number;
  recommendations: string[];
}

export interface TrendAnalysis {
  period: string;
  attendanceRate: number;
  change: number;
  factors: string[];
}

class AnalyticsService {
  /**
   * Récupère les données analytics complètes
   */
  async getAnalyticsData(period: 'week' | 'month' | 'year' = 'month'): Promise<AnalyticsData> {
    try {
      const response = await api.get(`/analytics/dashboard/?period=${period}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des analytics:', error);
      // Retourner des données mockées en cas d'erreur
      return this.getMockAnalyticsData();
    }
  }

  /**
   * Récupère les prédictions d'absence
   */
  async getAbsencePredictions(): Promise<PredictionData[]> {
    try {
      const response = await api.get('/analytics/predictions/absences/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des prédictions:', error);
      return [];
    }
  }

  /**
   * Récupère l'analyse des tendances
   */
  async getTrendAnalysis(startDate: string, endDate: string): Promise<TrendAnalysis[]> {
    try {
      const response = await api.get(`/analytics/trends/?start_date=${startDate}&end_date=${endDate}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des tendances:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques par classe
   */
  async getClassStatistics(classId?: number): Promise<any> {
    try {
      const url = classId ? `/analytics/classes/${classId}/` : '/analytics/classes/';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats par classe:', error);
      return null;
    }
  }

  /**
   * Récupère les données de performance en temps réel
   */
  async getRealTimeMetrics(): Promise<{
    currentAttendance: number;
    todayArrivals: number;
    lateArrivals: number;
    lastUpdate: string;
  }> {
    try {
      const response = await api.get('/analytics/realtime/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des métriques temps réel:', error);
      return {
        currentAttendance: 0,
        todayArrivals: 0,
        lateArrivals: 0,
        lastUpdate: new Date().toISOString()
      };
    }
  }

  /**
   * Exporte les données analytics
   */
  async exportAnalytics(
    format: 'pdf' | 'excel' | 'csv',
    period: 'week' | 'month' | 'year',
    includeCharts: boolean = true
  ): Promise<Blob> {
    try {
      const response = await api.get(`/analytics/export/`, {
        params: { format, period, include_charts: includeCharts },
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'export des analytics:', error);
      throw error;
    }
  }

  /**
   * Génère un rapport personnalisé
   */
  async generateCustomReport(config: {
    startDate: string;
    endDate: string;
    classes?: number[];
    students?: number[];
    metrics: string[];
    format: 'pdf' | 'excel';
  }): Promise<Blob> {
    try {
      const response = await api.post('/analytics/custom-report/', config, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      throw error;
    }
  }

  /**
   * Données mockées pour les tests
   */
  private getMockAnalyticsData(): AnalyticsData {
    return {
      attendanceRate: 87.5,
      totalStudents: 245,
      presentToday: 214,
      absentToday: 23,
      lateToday: 8,
      weeklyTrend: [85, 88, 82, 90, 87, 89, 86],
      monthlyStats: {
        labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
        present: [210, 215, 208, 214],
        absent: [25, 20, 27, 23],
        late: [10, 10, 10, 8]
      },
      classStats: [
        { className: 'CP1', attendanceRate: 92, totalStudents: 28 },
        { className: 'CP2', attendanceRate: 89, totalStudents: 30 },
        { className: 'CE1', attendanceRate: 85, totalStudents: 32 },
        { className: 'CE2', attendanceRate: 88, totalStudents: 29 },
        { className: 'CM1', attendanceRate: 84, totalStudents: 31 },
        { className: 'CM2', attendanceRate: 90, totalStudents: 35 }
      ],
      timeDistribution: {
        labels: ['7h-8h', '8h-9h', '9h-10h', '10h-11h', '11h-12h'],
        data: [15, 120, 85, 25, 10]
      },
      predictions: {
        nextWeekAttendance: 89,
        riskStudents: 12,
        trend: 'up'
      }
    };
  }

  /**
   * Calcule les métriques de performance
   */
  calculatePerformanceMetrics(data: AnalyticsData): {
    efficiency: number;
    improvement: number;
    riskLevel: 'low' | 'medium' | 'high';
  } {
    const efficiency = (data.attendanceRate / 100) * 100;
    const weeklyAverage = data.weeklyTrend.reduce((a, b) => a + b, 0) / data.weeklyTrend.length;
    const improvement = data.attendanceRate - weeklyAverage;
    
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (data.predictions.riskStudents > 20) riskLevel = 'high';
    else if (data.predictions.riskStudents > 10) riskLevel = 'medium';

    return { efficiency, improvement, riskLevel };
  }

  /**
   * Formate les données pour les graphiques
   */
  formatChartData(data: AnalyticsData, chartType: 'line' | 'bar' | 'doughnut' | 'radar') {
    switch (chartType) {
      case 'line':
        return {
          labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
          datasets: [{
            label: 'Taux de présence (%)',
            data: data.weeklyTrend,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            fill: true,
            tension: 0.4
          }]
        };
      
      case 'bar':
        return {
          labels: data.monthlyStats.labels,
          datasets: [
            {
              label: 'Présents',
              data: data.monthlyStats.present,
              backgroundColor: 'rgba(34, 197, 94, 0.8)',
            },
            {
              label: 'Absents',
              data: data.monthlyStats.absent,
              backgroundColor: 'rgba(239, 68, 68, 0.8)',
            },
            {
              label: 'En retard',
              data: data.monthlyStats.late,
              backgroundColor: 'rgba(245, 158, 11, 0.8)',
            }
          ]
        };
      
      case 'doughnut':
        return {
          labels: ['Présents', 'Absents', 'En retard'],
          datasets: [{
            data: [data.presentToday, data.absentToday, data.lateToday],
            backgroundColor: [
              'rgba(34, 197, 94, 0.8)',
              'rgba(239, 68, 68, 0.8)',
              'rgba(245, 158, 11, 0.8)'
            ],
            borderWidth: 2
          }]
        };
      
      case 'radar':
        return {
          labels: data.classStats.map(c => c.className),
          datasets: [{
            label: 'Taux de présence par classe (%)',
            data: data.classStats.map(c => c.attendanceRate),
            backgroundColor: 'rgba(59, 130, 246, 0.2)',
            borderColor: 'rgba(59, 130, 246, 1)',
            pointBackgroundColor: 'rgba(59, 130, 246, 1)',
          }]
        };
      
      default:
        return null;
    }
  }
}

export const analyticsService = new AnalyticsService();

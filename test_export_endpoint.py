#!/usr/bin/env python3
"""
Script de test pour vérifier que l'endpoint d'exportation des présences récentes fonctionne
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"

def test_export_endpoint():
    """Test de l'endpoint d'exportation des présences récentes"""
    
    print("🧪 Test de l'endpoint d'exportation des présences récentes")
    print("=" * 60)
    
    # Test 1: Vérifier que l'endpoint existe (sans authentification)
    print("\n1. Test de l'existence de l'endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/export/presences/recentes/?format=csv&limit=10")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text[:200]}...")
        
        if response.status_code == 401:
            print("   ✅ Endpoint trouvé (erreur d'authentification attendue)")
        elif response.status_code == 404:
            print("   ❌ Endpoint non trouvé")
            return False
        else:
            print(f"   ⚠️  Status inattendu: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Impossible de se connecter au serveur")
        return False
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    
    # Test 2: Vérifier les autres endpoints d'exportation
    print("\n2. Test des autres endpoints d'exportation...")
    
    endpoints_to_test = [
        "/export/presences/jour/",
        "/export/presences/classe/", 
        "/export/assiduite/etudiants/",
        "/export/alertes/absences/"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}?format=csv")
            print(f"   {endpoint}: Status {response.status_code}")
        except Exception as e:
            print(f"   {endpoint}: Erreur - {e}")
    
    print("\n✅ Tests terminés")
    return True

if __name__ == "__main__":
    test_export_endpoint()

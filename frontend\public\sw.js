// Service Worker Simple pour PWA - Gestion Présence Intelligente
const CACHE_NAME = 'gpi-v1.0.2';

// Ressources essentielles à mettre en cache
const STATIC_ASSETS = [
  '/',
  '/manifest.json'
];

// Installation du Service Worker
self.addEventListener('install', event => {
  console.log('[SW] Installation en cours...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('[SW] Cache créé');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Erreur installation:', error);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', event => {
  console.log('[SW] Activation en cours...');
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME) {
              console.log('[SW] Suppression ancien cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Activation terminée');
        return self.clients.claim();
      })
      .catch(error => {
        console.error('[SW] Erreur activation:', error);
      })
  );
});

// Interception des requêtes
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignorer les requêtes non-HTTP et les WebSockets
  if (!request.url.startsWith('http') || request.url.includes('ws://') || request.url.includes('wss://')) {
    return;
  }

  // Stratégie simple : Network First avec fallback cache
  event.respondWith(
    fetch(request)
      .then(response => {
        // Cloner la réponse pour la mettre en cache
        if (response.status === 200 && request.method === 'GET') {
          const responseClone = response.clone();
          caches.open(DYNAMIC_CACHE)
            .then(cache => {
              cache.put(request, responseClone);
            })
            .catch(error => {
              console.log('[SW] Erreur mise en cache:', error);
            });
        }
        return response;
      })
      .catch(error => {
        console.log('[SW] Erreur réseau, tentative cache:', error);
        // Fallback vers le cache
        return caches.match(request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }
            // Si pas de cache et c'est une page, retourner une réponse basique
            if (request.destination === 'document') {
              return new Response('Application hors ligne', {
                status: 200,
                statusText: 'OK',
                headers: { 'Content-Type': 'text/html' }
              });
            }
            throw error;
          });
      })
  );
});

// Gestion des notifications push
self.addEventListener('push', event => {
  console.log('[SW] Notification push reçue');
  
  const options = {
    body: 'Nouvelle notification de présence',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Voir détails',
        icon: '/icons/action-explore.png'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/icons/action-close.png'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.title = data.title || 'Gestion Présence';
  }

  event.waitUntil(
    self.registration.showNotification('Gestion Présence', options)
  );
});

// Gestion des clics sur les notifications
self.addEventListener('notificationclick', event => {
  console.log('[SW] Clic sur notification');
  
  event.notification.close();

  if (event.action === 'explore') {
    // Ouvrir l'application sur la page appropriée
    event.waitUntil(
      clients.openWindow('/dashboard')
    );
  } else if (event.action === 'close') {
    // Fermer la notification
    event.notification.close();
  } else {
    // Clic sur la notification principale
    event.waitUntil(
      clients.matchAll()
        .then(clientList => {
          if (clientList.length > 0) {
            return clientList[0].focus();
          }
          return clients.openWindow('/');
        })
    );
  }
});

// Synchronisation en arrière-plan
self.addEventListener('sync', event => {
  console.log('[SW] Synchronisation en arrière-plan:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Synchroniser les données en attente
      syncPendingData()
    );
  }
});

// Fonction pour synchroniser les données en attente
async function syncPendingData() {
  try {
    // Récupérer les données en attente depuis IndexedDB
    const pendingData = await getPendingData();
    
    for (const data of pendingData) {
      try {
        await fetch('/api/sync/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });
        
        // Supprimer les données synchronisées
        await removePendingData(data.id);
      } catch (error) {
        console.error('[SW] Erreur lors de la synchronisation:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Erreur lors de la récupération des données en attente:', error);
  }
}

// Fonctions utilitaires pour IndexedDB (à implémenter)
async function getPendingData() {
  // Implémentation pour récupérer les données en attente
  return [];
}

async function removePendingData(id) {
  // Implémentation pour supprimer les données synchronisées
  console.log('Données supprimées:', id);
}

// Gestion des erreurs
self.addEventListener('error', event => {
  console.error('[SW] Erreur:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('[SW] Promesse rejetée:', event.reason);
  // Empêcher l'erreur de remonter
  event.preventDefault();
});

console.log('[SW] Service Worker chargé et prêt');

// Service Worker pour PWA - Gestion Présence Intelligente
const CACHE_NAME = 'gpi-v1.0.1';
const STATIC_CACHE = 'gpi-static-v1.0.1';
const DYNAMIC_CACHE = 'gpi-dynamic-v1.0.1';

// Ressources à mettre en cache immédiatement
const STATIC_ASSETS = [
  '/',
  '/dashboard',
  '/recognition',
  '/students',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Ressources à mettre en cache dynamiquement
const DYNAMIC_ASSETS_PATTERNS = [
  /^\/api\//,
  /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
  /\.(?:js|css)$/
];

// Installation du Service Worker
self.addEventListener('install', event => {
  console.log('[SW] Installation en cours...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Mise en cache des ressources statiques');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('[SW] Installation terminée');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Erreur lors de l\'installation:', error);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', event => {
  console.log('[SW] Activation en cours...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Suppression de l\'ancien cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Activation terminée');
        return self.clients.claim();
      })
  );
});

// Interception des requêtes
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) {
    return;
  }

  // Stratégie Cache First pour les ressources statiques
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then(response => {
          return response || fetch(request);
        })
    );
    return;
  }

  // Stratégie Network First pour les API
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then(response => {
          // Mettre en cache les réponses réussies
          if (response.status === 200) {
            const responseClone = response.clone();
            caches.open(DYNAMIC_CACHE)
              .then(cache => {
                cache.put(request, responseClone);
              });
          }
          return response;
        })
        .catch(() => {
          // Fallback vers le cache en cas d'erreur réseau
          return caches.match(request);
        })
    );
    return;
  }

  // Stratégie Stale While Revalidate pour les autres ressources
  event.respondWith(
    caches.match(request)
      .then(response => {
        const fetchPromise = fetch(request)
          .then(networkResponse => {
            // Mettre à jour le cache avec la nouvelle réponse
            if (networkResponse.status === 200) {
              const responseClone = networkResponse.clone();
              caches.open(DYNAMIC_CACHE)
                .then(cache => {
                  cache.put(request, responseClone);
                });
            }
            return networkResponse;
          })
          .catch(() => {
            // En cas d'erreur réseau, retourner une page offline si disponible
            if (request.destination === 'document') {
              return caches.match('/offline.html');
            }
          });

        // Retourner immédiatement la réponse du cache si disponible
        return response || fetchPromise;
      })
  );
});

// Gestion des notifications push
self.addEventListener('push', event => {
  console.log('[SW] Notification push reçue');
  
  const options = {
    body: 'Nouvelle notification de présence',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Voir détails',
        icon: '/icons/action-explore.png'
      },
      {
        action: 'close',
        title: 'Fermer',
        icon: '/icons/action-close.png'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.title = data.title || 'Gestion Présence';
  }

  event.waitUntil(
    self.registration.showNotification('Gestion Présence', options)
  );
});

// Gestion des clics sur les notifications
self.addEventListener('notificationclick', event => {
  console.log('[SW] Clic sur notification');
  
  event.notification.close();

  if (event.action === 'explore') {
    // Ouvrir l'application sur la page appropriée
    event.waitUntil(
      clients.openWindow('/dashboard')
    );
  } else if (event.action === 'close') {
    // Fermer la notification
    event.notification.close();
  } else {
    // Clic sur la notification principale
    event.waitUntil(
      clients.matchAll()
        .then(clientList => {
          if (clientList.length > 0) {
            return clientList[0].focus();
          }
          return clients.openWindow('/');
        })
    );
  }
});

// Synchronisation en arrière-plan
self.addEventListener('sync', event => {
  console.log('[SW] Synchronisation en arrière-plan:', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Synchroniser les données en attente
      syncPendingData()
    );
  }
});

// Fonction pour synchroniser les données en attente
async function syncPendingData() {
  try {
    // Récupérer les données en attente depuis IndexedDB
    const pendingData = await getPendingData();
    
    for (const data of pendingData) {
      try {
        await fetch('/api/sync/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });
        
        // Supprimer les données synchronisées
        await removePendingData(data.id);
      } catch (error) {
        console.error('[SW] Erreur lors de la synchronisation:', error);
      }
    }
  } catch (error) {
    console.error('[SW] Erreur lors de la récupération des données en attente:', error);
  }
}

// Fonctions utilitaires pour IndexedDB (à implémenter)
async function getPendingData() {
  // Implémentation pour récupérer les données en attente
  return [];
}

async function removePendingData(id) {
  // Implémentation pour supprimer les données synchronisées
  console.log('Données supprimées:', id);
}

// Gestion des erreurs
self.addEventListener('error', event => {
  console.error('[SW] Erreur:', event.error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('[SW] Promesse rejetée:', event.reason);
});

console.log('[SW] Service Worker chargé et prêt');

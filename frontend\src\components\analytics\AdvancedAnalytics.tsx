import React, { useState, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
} from 'chart.js';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { CalendarIcon, ClockIcon, UserGroupIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

// Enregistrer les composants Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

interface AnalyticsData {
  attendanceRate: number;
  totalStudents: number;
  presentToday: number;
  absentToday: number;
  lateToday: number;
  weeklyTrend: number[];
  monthlyStats: {
    labels: string[];
    present: number[];
    absent: number[];
    late: number[];
  };
  classStats: {
    className: string;
    attendanceRate: number;
    totalStudents: number;
  }[];
  timeDistribution: {
    labels: string[];
    data: number[];
  };
  predictions: {
    nextWeekAttendance: number;
    riskStudents: number;
    trend: 'up' | 'down' | 'stable';
  };
}

const AdvancedAnalytics: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      // Simuler des données analytics (à remplacer par un vrai appel API)
      const mockData: AnalyticsData = {
        attendanceRate: 87.5,
        totalStudents: 245,
        presentToday: 214,
        absentToday: 23,
        lateToday: 8,
        weeklyTrend: [85, 88, 82, 90, 87, 89, 86],
        monthlyStats: {
          labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
          present: [210, 215, 208, 214],
          absent: [25, 20, 27, 23],
          late: [10, 10, 10, 8]
        },
        classStats: [
          { className: 'CP1', attendanceRate: 92, totalStudents: 28 },
          { className: 'CP2', attendanceRate: 89, totalStudents: 30 },
          { className: 'CE1', attendanceRate: 85, totalStudents: 32 },
          { className: 'CE2', attendanceRate: 88, totalStudents: 29 },
          { className: 'CM1', attendanceRate: 84, totalStudents: 31 },
          { className: 'CM2', attendanceRate: 90, totalStudents: 35 }
        ],
        timeDistribution: {
          labels: ['7h-8h', '8h-9h', '9h-10h', '10h-11h', '11h-12h'],
          data: [15, 120, 85, 25, 10]
        },
        predictions: {
          nextWeekAttendance: 89,
          riskStudents: 12,
          trend: 'up'
        }
      };

      setTimeout(() => {
        setAnalyticsData(mockData);
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des analytics:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">Impossible de charger les données analytics</p>
      </div>
    );
  }

  // Configuration des graphiques
  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Tendance de présence hebdomadaire'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100
      }
    }
  };

  const lineChartData = {
    labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
    datasets: [
      {
        label: 'Taux de présence (%)',
        data: analyticsData.weeklyTrend,
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: true,
        tension: 0.4
      }
    ]
  };

  const barChartData = {
    labels: analyticsData.monthlyStats.labels,
    datasets: [
      {
        label: 'Présents',
        data: analyticsData.monthlyStats.present,
        backgroundColor: 'rgba(34, 197, 94, 0.8)',
      },
      {
        label: 'Absents',
        data: analyticsData.monthlyStats.absent,
        backgroundColor: 'rgba(239, 68, 68, 0.8)',
      },
      {
        label: 'En retard',
        data: analyticsData.monthlyStats.late,
        backgroundColor: 'rgba(245, 158, 11, 0.8)',
      }
    ]
  };

  const doughnutData = {
    labels: ['Présents', 'Absents', 'En retard'],
    datasets: [
      {
        data: [analyticsData.presentToday, analyticsData.absentToday, analyticsData.lateToday],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ],
        borderWidth: 2
      }
    ]
  };

  const radarData = {
    labels: analyticsData.classStats.map(c => c.className),
    datasets: [
      {
        label: 'Taux de présence par classe (%)',
        data: analyticsData.classStats.map(c => c.attendanceRate),
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
      }
    ]
  };

  return (
    <div className="space-y-6">
      {/* En-tête avec sélecteur de période */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Analytics Avancées
        </h2>
        <div className="flex space-x-2">
          {(['week', 'month', 'year'] as const).map((period) => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedPeriod === period
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {period === 'week' ? 'Semaine' : period === 'month' ? 'Mois' : 'Année'}
            </button>
          ))}
        </div>
      </div>

      {/* KPIs principaux */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUpIcon className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Taux de présence</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {analyticsData.attendanceRate}%
              </p>
              <p className="text-xs text-green-600">
                {analyticsData.predictions.trend === 'up' ? '+2.3%' : '-1.2%'} vs semaine dernière
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UserGroupIcon className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Présents aujourd'hui</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {analyticsData.presentToday}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                sur {analyticsData.totalStudents} étudiants
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ClockIcon className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">En retard</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {analyticsData.lateToday}
              </p>
              <p className="text-xs text-orange-600">
                {((analyticsData.lateToday / analyticsData.totalStudents) * 100).toFixed(1)}% du total
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Prédiction semaine prochaine</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                {analyticsData.predictions.nextWeekAttendance}%
              </p>
              <p className="text-xs text-purple-600">
                {analyticsData.predictions.riskStudents} étudiants à risque
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tendance hebdomadaire */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Tendance Hebdomadaire
          </h3>
          <Line data={lineChartData} options={lineChartOptions} />
        </div>

        {/* Répartition du jour */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Répartition Aujourd'hui
          </h3>
          <div className="h-64 flex items-center justify-center">
            <Doughnut data={doughnutData} />
          </div>
        </div>

        {/* Statistiques mensuelles */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Évolution Mensuelle
          </h3>
          <Bar data={barChartData} />
        </div>

        {/* Performance par classe */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Performance par Classe
          </h3>
          <div className="h-64 flex items-center justify-center">
            <Radar data={radarData} />
          </div>
        </div>
      </div>

      {/* Tableau détaillé par classe */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Détail par Classe
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Classe
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total Étudiants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Taux de Présence
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Tendance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {analyticsData.classStats.map((classData, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {classData.className}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {classData.totalStudents}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${classData.attendanceRate}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-900 dark:text-white">
                        {classData.attendanceRate}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {classData.attendanceRate > 85 ? (
                      <ArrowTrendingUpIcon className="h-5 w-5 text-green-500" />
                    ) : (
                      <ArrowTrendingDownIcon className="h-5 w-5 text-red-500" />
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdvancedAnalytics;

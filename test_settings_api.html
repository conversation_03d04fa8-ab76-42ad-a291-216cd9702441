<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Settings</h1>
        <p>Cette page teste l'endpoint des paramètres sans authentification (pour vérifier la structure).</p>
        
        <div class="test-section info">
            <h3>📋 Informations</h3>
            <p><strong>URL Backend:</strong> http://127.0.0.1:8000/api/settings/</p>
            <p><strong>Status:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>🔍 Test GET /api/settings/</h3>
            <button onclick="testGetSettings()">Tester GET</button>
            <div id="get-result"></div>
        </div>

        <div class="test-section">
            <h3>📤 Test POST /api/settings/ (Sécurité)</h3>
            <button onclick="testPostSecurity()">Tester POST Sécurité</button>
            <div id="post-security-result"></div>
        </div>

        <div class="test-section">
            <h3>📤 Test POST /api/settings/ (Général)</h3>
            <button onclick="testPostGeneral()">Tester POST Général</button>
            <div id="post-general-result"></div>
        </div>
    </div>

    <script>
        // Vérifier le status du serveur
        async function checkServerStatus() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/settings/');
                if (response.status === 401) {
                    document.getElementById('server-status').innerHTML = '✅ Serveur actif (authentification requise)';
                    document.getElementById('server-status').style.color = 'green';
                } else {
                    document.getElementById('server-status').innerHTML = `⚠️ Serveur actif (status: ${response.status})`;
                    document.getElementById('server-status').style.color = 'orange';
                }
            } catch (error) {
                document.getElementById('server-status').innerHTML = '❌ Serveur non accessible';
                document.getElementById('server-status').style.color = 'red';
            }
        }

        async function testGetSettings() {
            const resultDiv = document.getElementById('get-result');
            resultDiv.innerHTML = '<p>⏳ Test en cours...</p>';
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/settings/');
                const data = await response.text();
                
                resultDiv.innerHTML = `
                    <h4>Résultat:</h4>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <p><strong>Réponse:</strong></p>
                    <pre>${data}</pre>
                `;
                
                if (response.status === 401) {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML += '<p>✅ Comportement attendu: authentification requise</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Erreur: ${error.message}</p>`;
                resultDiv.className = 'error';
            }
        }

        async function testPostSecurity() {
            const resultDiv = document.getElementById('post-security-result');
            resultDiv.innerHTML = '<p>⏳ Test en cours...</p>';
            
            const testData = {
                face_recognition_confidence_threshold: 85
            };
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/settings/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.text();
                
                resultDiv.innerHTML = `
                    <h4>Résultat:</h4>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <p><strong>Données envoyées:</strong></p>
                    <pre>${JSON.stringify(testData, null, 2)}</pre>
                    <p><strong>Réponse:</strong></p>
                    <pre>${data}</pre>
                `;
                
                if (response.status === 401) {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML += '<p>✅ Comportement attendu: authentification requise</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Erreur: ${error.message}</p>`;
                resultDiv.className = 'error';
            }
        }

        async function testPostGeneral() {
            const resultDiv = document.getElementById('post-general-result');
            resultDiv.innerHTML = '<p>⏳ Test en cours...</p>';
            
            const testData = {
                schoolName: 'École Test',
                address: '123 Rue Test',
                phone: '+33123456789',
                email: '<EMAIL>'
            };
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/settings/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.text();
                
                resultDiv.innerHTML = `
                    <h4>Résultat:</h4>
                    <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                    <p><strong>Données envoyées:</strong></p>
                    <pre>${JSON.stringify(testData, null, 2)}</pre>
                    <p><strong>Réponse:</strong></p>
                    <pre>${data}</pre>
                `;
                
                if (response.status === 401) {
                    resultDiv.className = 'success';
                    resultDiv.innerHTML += '<p>✅ Comportement attendu: authentification requise</p>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Erreur: ${error.message}</p>`;
                resultDiv.className = 'error';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

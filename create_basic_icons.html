<!DOCTYPE html>
<html>
<head>
    <title>Créer Icônes PWA</title>
</head>
<body>
    <h1>Création d'Icônes PWA Basiques</h1>
    <p>Cliquez sur les boutons pour télécharger les icônes nécessaires :</p>
    
    <canvas id="canvas" style="display: none;"></canvas>
    
    <div>
        <button onclick="createIcon(72)">📱 Créer icon-72x72.png</button>
        <button onclick="createIcon(96)">📱 Créer icon-96x96.png</button>
        <button onclick="createIcon(128)">📱 Créer icon-128x128.png</button>
        <button onclick="createIcon(144)">📱 Créer icon-144x144.png</button>
        <button onclick="createIcon(152)">📱 Créer icon-152x152.png</button>
        <button onclick="createIcon(192)">📱 Créer icon-192x192.png</button>
        <button onclick="createIcon(384)">📱 Créer icon-384x384.png</button>
        <button onclick="createIcon(512)">📱 Créer icon-512x512.png</button>
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="createAllIcons()" style="background: #667eea; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px;">
            🚀 Créer Toutes les Icônes
        </button>
    </div>
    
    <div id="status" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;"></div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Configurer le canvas
            canvas.width = size;
            canvas.height = size;
            
            // Fond dégradé
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Coins arrondis (simulés)
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.1);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Cercle central blanc
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.25;
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Texte "GPI"
            ctx.fillStyle = '#667eea';
            ctx.font = `bold ${size * 0.12}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('GPI', centerX, centerY);
            
            // Petits éléments décoratifs
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const dotSize = size * 0.015;
            
            // Points en haut
            for (let i = 0; i < 3; i++) {
                const x = centerX + (i - 1) * size * 0.08;
                const y = centerY - size * 0.15;
                ctx.beginPath();
                ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // Élément en bas (représentant la reconnaissance)
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY + size * 0.12, size * 0.06, size * 0.03, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Télécharger l'image
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `icon-${size}x${size}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                
                updateStatus(`✅ Icône ${size}x${size} créée et téléchargée`);
            }, 'image/png');
        }
        
        function createAllIcons() {
            const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
            let index = 0;
            
            updateStatus('🚀 Création de toutes les icônes en cours...');
            
            function createNext() {
                if (index < sizes.length) {
                    createIcon(sizes[index]);
                    index++;
                    setTimeout(createNext, 500); // Délai entre chaque création
                } else {
                    updateStatus('🎉 Toutes les icônes ont été créées ! Placez-les dans le dossier frontend/public/icons/');
                }
            }
            
            createNext();
        }
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }
        
        // Polyfill pour roundRect si nécessaire
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
        
        // Message d'instructions
        updateStatus('📱 Cliquez sur "Créer Toutes les Icônes" pour générer toutes les tailles nécessaires pour la PWA');
    </script>
</body>
</html>

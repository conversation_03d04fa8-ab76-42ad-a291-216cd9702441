<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Notifications Automatiques Arrivée/Départ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 Test - Notifications Automatiques Arrivée/Départ</h1>
        <p>Test complet du système de notifications automatiques pour les parents.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Frontend:</strong> http://localhost:5174/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>👥 Configuration des Parents</h3>
            <p>Configurez un parent pour recevoir les notifications :</p>
            
            <div class="form-group">
                <label for="student-select">Étudiant :</label>
                <select id="student-select">
                    <option value="">Chargement...</option>
                </select>
                <button onclick="loadStudents()">🔄 Recharger</button>
            </div>

            <div class="form-group">
                <label for="parent-select">Parent :</label>
                <select id="parent-select">
                    <option value="">Sélectionnez d'abord un étudiant</option>
                </select>
            </div>

            <div class="step">
                <h4>Paramètres de notification</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label>
                            <input type="checkbox" id="notifications-sms"> Notifications SMS
                        </label>
                        <label>
                            <input type="checkbox" id="notifications-email"> Notifications Email
                        </label>
                    </div>
                    <div>
                        <label>
                            <input type="checkbox" id="notifications-arrivee" checked> Notifications d'arrivée
                        </label>
                        <label>
                            <input type="checkbox" id="notifications-depart" checked> Notifications de départ
                        </label>
                    </div>
                </div>
                <button onclick="updateParentSettings()" class="btn-success">💾 Sauvegarder paramètres</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Tests de Reconnaissance avec Notifications</h3>
            <p>Testez la reconnaissance faciale avec envoi automatique de notifications :</p>
            
            <div class="step">
                <h4>Test Arrivée</h4>
                <button onclick="testArrivalWithNotification()" class="btn-success">🟢 Tester Arrivée + Notification</button>
                <div id="arrival-result"></div>
            </div>

            <div class="step">
                <h4>Test Départ</h4>
                <button onclick="testDepartureWithNotification()" class="btn-warning">🟠 Tester Départ + Notification</button>
                <div id="departure-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📧 Tests Manuels de Notifications</h3>
            <button onclick="testManualSMS()">📱 Test SMS Manuel</button>
            <button onclick="testManualEmail()">📧 Test Email Manuel</button>
            <button onclick="checkNotificationHistory()">📋 Historique Notifications</button>
        </div>

        <div class="test-section">
            <h3>⚙️ Paramètres Globaux</h3>
            <button onclick="checkGlobalSettings()">🔧 Vérifier Paramètres Globaux</button>
            <button onclick="toggleAutomaticNotifications()">🔄 Activer/Désactiver Notifications Auto</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;
        let students = [];
        let selectedStudent = null;
        let selectedParent = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Charger la liste des étudiants
        async function loadStudents() {
            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    students = await response.json();
                    const select = document.getElementById('student-select');
                    select.innerHTML = '<option value="">Sélectionnez un étudiant</option>';
                    
                    students.forEach(student => {
                        const option = document.createElement('option');
                        option.value = student.id;
                        option.textContent = `${student.prenom} ${student.nom} (ID: ${student.id})`;
                        select.appendChild(option);
                    });

                    // Event listener pour charger les parents
                    select.addEventListener('change', loadParents);
                } else {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('Erreur lors du chargement des étudiants:', error);
                document.getElementById('student-select').innerHTML = '<option value="">Erreur de chargement</option>';
            }
        }

        // Charger les parents de l'étudiant sélectionné
        async function loadParents() {
            const studentId = document.getElementById('student-select').value;
            const parentSelect = document.getElementById('parent-select');
            
            if (!studentId) {
                parentSelect.innerHTML = '<option value="">Sélectionnez d\'abord un étudiant</option>';
                return;
            }

            selectedStudent = students.find(s => s.id == studentId);

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/parents/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const parents = await response.json();
                    parentSelect.innerHTML = '<option value="">Sélectionnez un parent</option>';
                    
                    parents.forEach(parent => {
                        const option = document.createElement('option');
                        option.value = parent.id;
                        option.textContent = `${parent.prenom} ${parent.nom} (${parent.relation})`;
                        option.dataset.parent = JSON.stringify(parent);
                        parentSelect.appendChild(option);
                    });

                    // Event listener pour charger les paramètres du parent
                    parentSelect.addEventListener('change', loadParentSettings);
                } else {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('Erreur lors du chargement des parents:', error);
                parentSelect.innerHTML = '<option value="">Erreur de chargement</option>';
            }
        }

        // Charger les paramètres du parent sélectionné
        function loadParentSettings() {
            const parentSelect = document.getElementById('parent-select');
            const selectedOption = parentSelect.options[parentSelect.selectedIndex];
            
            if (!selectedOption.dataset.parent) return;

            selectedParent = JSON.parse(selectedOption.dataset.parent);
            
            // Mettre à jour les checkboxes
            document.getElementById('notifications-sms').checked = selectedParent.notifications_sms || false;
            document.getElementById('notifications-email').checked = selectedParent.notifications_email || false;
            document.getElementById('notifications-arrivee').checked = selectedParent.notifications_arrivee || false;
            document.getElementById('notifications-depart').checked = selectedParent.notifications_depart || false;
        }

        // Mettre à jour les paramètres du parent
        async function updateParentSettings() {
            if (!selectedParent) {
                alert('Veuillez sélectionner un parent');
                return;
            }

            const settings = {
                notifications_sms: document.getElementById('notifications-sms').checked,
                notifications_email: document.getElementById('notifications-email').checked,
                notifications_arrivee: document.getElementById('notifications-arrivee').checked,
                notifications_depart: document.getElementById('notifications-depart').checked,
            };

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/parents/${selectedParent.id}/`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(settings)
                });

                if (response.ok) {
                    const updatedParent = await response.json();
                    selectedParent = updatedParent;
                    
                    document.getElementById('result').innerHTML = `
                        <div class="success">
                            <h3>✅ Paramètres mis à jour</h3>
                            <p>Les paramètres de notification de ${selectedParent.prenom} ${selectedParent.nom} ont été mis à jour.</p>
                        </div>
                    `;
                } else {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test d'arrivée avec notification
        async function testArrivalWithNotification() {
            if (!selectedStudent) {
                alert('Veuillez sélectionner un étudiant');
                return;
            }

            const resultDiv = document.getElementById('arrival-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test d\'arrivée avec notification...</p></div>';

            try {
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'arrivee'
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Test d'arrivée réussi</h4>
                            <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Heure:</strong> ${result.presence_time}</p>
                            ${result.recognized ? '<p><strong>📧 Notifications automatiques envoyées aux parents configurés</strong></p>' : ''}
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Erreur d'arrivée</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test de départ avec notification
        async function testDepartureWithNotification() {
            if (!selectedStudent) {
                alert('Veuillez sélectionner un étudiant');
                return;
            }

            const resultDiv = document.getElementById('departure-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de départ avec notification...</p></div>';

            try {
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'depart'
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Test de départ réussi</h4>
                            <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Heure:</strong> ${result.presence_time}</p>
                            ${result.recognized ? '<p><strong>📧 Notifications automatiques envoyées aux parents configurés</strong></p>' : ''}
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Erreur de départ</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test SMS manuel
        async function testManualSMS() {
            if (!selectedParent) {
                alert('Veuillez sélectionner un parent');
                return;
            }

            const message = `Test de notification SMS pour ${selectedStudent.prenom} ${selectedStudent.nom}`;

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/parents/${selectedParent.id}/send_sms/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ message: message })
                });

                const result = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <div class="${result.success ? 'success' : 'error'}">
                        <h3>📱 Test SMS</h3>
                        <p><strong>Succès:</strong> ${result.success ? 'Oui' : 'Non'}</p>
                        <p><strong>Message:</strong> ${result.message}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test Email manuel
        async function testManualEmail() {
            if (!selectedParent) {
                alert('Veuillez sélectionner un parent');
                return;
            }

            const subject = `Test de notification - ${selectedStudent.prenom} ${selectedStudent.nom}`;
            const message = `Ceci est un test de notification par email pour votre enfant ${selectedStudent.prenom} ${selectedStudent.nom}.`;

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/parents/${selectedParent.id}/send_email/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ 
                        subject: subject,
                        message: message 
                    })
                });

                const result = await response.json();
                
                document.getElementById('result').innerHTML = `
                    <div class="${result.success ? 'success' : 'error'}">
                        <h3>📧 Test Email</h3>
                        <p><strong>Succès:</strong> ${result.success ? 'Oui' : 'Non'}</p>
                        <p><strong>Message:</strong> ${result.message}</p>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier l'historique des notifications
        async function checkNotificationHistory() {
            document.getElementById('result').innerHTML = '<div class="info"><p>⏳ Récupération de l\'historique...</p></div>';

            try {
                // Pour l'instant, on simule car l'API n'existe pas encore
                document.getElementById('result').innerHTML = `
                    <div class="info">
                        <h3>📋 Historique des Notifications</h3>
                        <p>Cette fonctionnalité sera disponible prochainement.</p>
                        <p>Elle permettra de voir :</p>
                        <ul>
                            <li>Toutes les notifications envoyées</li>
                            <li>Statut de livraison (succès/échec)</li>
                            <li>Filtrage par parent, étudiant, type</li>
                            <li>Statistiques d'envoi</li>
                        </ul>
                    </div>
                `;

            } catch (error) {
                document.getElementById('result').innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les paramètres globaux
        async function checkGlobalSettings() {
            document.getElementById('result').innerHTML = `
                <div class="info">
                    <h3>⚙️ Paramètres Globaux</h3>
                    <p><strong>Notifications automatiques:</strong> ✅ Activées</p>
                    <p><strong>Notifications d'arrivée par défaut:</strong> ✅ Activées</p>
                    <p><strong>Notifications de départ par défaut:</strong> ✅ Activées</p>
                    <p><strong>Provider SMS:</strong> Configuration requise</p>
                    <p><strong>Provider Email:</strong> Configuration requise</p>
                </div>
            `;
        }

        // Activer/Désactiver les notifications automatiques
        async function toggleAutomaticNotifications() {
            document.getElementById('result').innerHTML = `
                <div class="warning">
                    <h3>🔄 Notifications Automatiques</h3>
                    <p>Les notifications automatiques sont actuellement <strong>activées</strong>.</p>
                    <p>Elles se déclenchent automatiquement lors de :</p>
                    <ul>
                        <li>🟢 Reconnaissance d'arrivée</li>
                        <li>🟠 Reconnaissance de départ</li>
                    </ul>
                    <p>Les notifications sont envoyées selon les paramètres de chaque parent.</p>
                </div>
            `;
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Backend actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Backend répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Backend inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Initialisation
        checkServerStatus();
        loadStudents();
    </script>
</body>
</html>

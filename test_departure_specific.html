<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Spécifique Départ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-arrival {
            background-color: #28a745;
        }
        .btn-departure {
            background-color: #fd7e14;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Spécifique Départ</h1>
        <p>Test détaillé du problème de tracking des départs.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Test complet Arrivée → Départ</h3>
            <p>Ce test va simuler un étudiant qui arrive puis repart :</p>
            
            <div class="step">
                <h4>Étape 1 : Créer un étudiant de test</h4>
                <button onclick="createTestStudent()">👤 Créer étudiant test</button>
                <div id="step1-result"></div>
            </div>

            <div class="step">
                <h4>Étape 2 : Enregistrer l'arrivée</h4>
                <button onclick="testArrival()" id="btn-arrival" disabled>🟢 Tester Arrivée</button>
                <div id="step2-result"></div>
            </div>

            <div class="step">
                <h4>Étape 3 : Enregistrer le départ</h4>
                <button onclick="testDeparture()" id="btn-departure" disabled>🟠 Tester Départ</button>
                <div id="step3-result"></div>
            </div>

            <div class="step">
                <h4>Étape 4 : Vérifier la présence finale</h4>
                <button onclick="checkFinalPresence()" id="btn-check" disabled>📊 Vérifier présence</button>
                <div id="step4-result"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Tests manuels</h3>
            <button onclick="testDirectDeparture()">🚪 Test départ direct (sans arrivée)</button>
            <button onclick="checkAllPresences()">📋 Voir toutes les présences</button>
            <button onclick="resetTest()">🔄 Reset test</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;
        let testStudentId = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Étape 1 : Créer un étudiant de test
        async function createTestStudent() {
            const resultDiv = document.getElementById('step1-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Création de l\'étudiant test...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Créer un étudiant de test
                const studentData = {
                    nom: 'TestDepart',
                    prenom: 'Etudiant',
                    date_naissance: '2010-01-01',
                    sexe: 'M',
                    statut: 'actif',
                    classe: 1
                };

                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(studentData)
                });

                if (response.ok) {
                    const student = await response.json();
                    testStudentId = student.id;
                    
                    // Enregistrer une photo pour la reconnaissance
                    const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                    
                    await fetch(`http://127.0.0.1:8000/api/etudiants/${testStudentId}/register_face/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify({
                            image: testImageBase64
                        })
                    });

                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Étudiant créé avec succès</h4>
                            <p><strong>ID:</strong> ${student.id}</p>
                            <p><strong>Nom:</strong> ${student.nom} ${student.prenom}</p>
                            <p>Photo enregistrée pour la reconnaissance faciale</p>
                        </div>
                    `;

                    // Activer le bouton suivant
                    document.getElementById('btn-arrival').disabled = false;
                } else {
                    const errorData = await response.text();
                    throw new Error(`Erreur ${response.status}: ${errorData}`);
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Étape 2 : Test d'arrivée
        async function testArrival() {
            const resultDiv = document.getElementById('step2-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test d\'arrivée...</p></div>';

            try {
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'arrivee'
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Test d'arrivée réussi</h4>
                            <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                            <p><strong>Mode:</strong> ${result.mode}</p>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Déjà présent:</strong> ${result.already_present ? 'Oui' : 'Non'}</p>
                            <p><strong>Heure:</strong> ${result.presence_time}</p>
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;

                    // Activer le bouton suivant
                    document.getElementById('btn-departure').disabled = false;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Erreur d'arrivée</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Étape 3 : Test de départ
        async function testDeparture() {
            const resultDiv = document.getElementById('step3-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de départ...</p></div>';

            try {
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'depart'
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Test de départ réussi</h4>
                            <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                            <p><strong>Mode:</strong> ${result.mode}</p>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Déjà présent:</strong> ${result.already_present ? 'Oui' : 'Non'}</p>
                            <p><strong>Heure:</strong> ${result.presence_time}</p>
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;

                    // Activer le bouton suivant
                    document.getElementById('btn-check').disabled = false;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Erreur de départ</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Étape 4 : Vérifier la présence finale
        async function checkFinalPresence() {
            const resultDiv = document.getElementById('step4-result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification de la présence...</p></div>';

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${testStudentId}/presences/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    if (presences.length > 0) {
                        const todayPresence = presences[0]; // La plus récente
                        
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>📊 Présence vérifiée</h4>
                                <p><strong>Date:</strong> ${todayPresence.date}</p>
                                <p><strong>Arrivée:</strong> ${todayPresence.heure_arrivee || '❌ Non enregistrée'}</p>
                                <p><strong>Départ:</strong> ${todayPresence.heure_depart || '❌ Non enregistré'}</p>
                                <p><strong>Statut:</strong> ${todayPresence.statut}</p>
                                
                                ${todayPresence.heure_arrivee && todayPresence.heure_depart ? 
                                    '<div class="success"><strong>✅ SUCCÈS : Arrivée ET départ enregistrés !</strong></div>' :
                                    '<div class="warning"><strong>⚠️ PROBLÈME : Départ manquant</strong></div>'
                                }
                                
                                <h5>Détails complets:</h5>
                                <pre>${JSON.stringify(todayPresence, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">
                                <h4>❌ Aucune présence trouvée</h4>
                                <p>L'étudiant n'a aucune présence enregistrée.</p>
                            </div>
                        `;
                    }
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test de départ direct (sans arrivée)
        async function testDirectDeparture() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de départ direct...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'depart'
                    })
                });

                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="info">
                        <h3>🚪 Test départ direct</h3>
                        <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                        <p><strong>Mode:</strong> ${result.mode}</p>
                        <p><strong>Message:</strong> ${result.message}</p>
                        <h4>Réponse complète:</h4>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Voir toutes les présences
        async function checkAllPresences() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Récupération de toutes les présences...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="info"><h3>📋 Toutes les présences (${presences.length})</h3></div>`;
                    
                    presences.slice(0, 10).forEach(presence => {
                        const hasArrival = presence.heure_arrivee;
                        const hasDeparture = presence.heure_depart;
                        
                        html += `
                            <div class="step">
                                <h4>${presence.etudiant_nom || 'ID ' + presence.etudiant} - ${presence.date}</h4>
                                <p><strong>Arrivée:</strong> ${hasArrival ? `✅ ${presence.heure_arrivee}` : '❌ Non enregistrée'}</p>
                                <p><strong>Départ:</strong> ${hasDeparture ? `✅ ${presence.heure_depart}` : '❌ Non enregistré'}</p>
                                <p><strong>Statut:</strong> ${presence.statut}</p>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Reset du test
        async function resetTest() {
            if (testStudentId) {
                try {
                    await fetch(`http://127.0.0.1:8000/api/etudiants/${testStudentId}/`, {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                } catch (error) {
                    console.error('Erreur lors de la suppression:', error);
                }
            }
            
            testStudentId = null;
            document.getElementById('btn-arrival').disabled = true;
            document.getElementById('btn-departure').disabled = true;
            document.getElementById('btn-check').disabled = true;
            
            // Clear results
            ['step1-result', 'step2-result', 'step3-result', 'step4-result', 'result'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            alert('Test réinitialisé');
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

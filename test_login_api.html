<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Login</h1>
        <p>Test de connexion à l'API d'authentification Django.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>URL API:</strong> http://127.0.0.1:8000/api/auth/token/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="form-group">
            <label for="username">Nom d'utilisateur:</label>
            <input type="text" id="username" value="admin">
        </div>

        <div class="form-group">
            <label for="password">Mot de passe:</label>
            <input type="password" id="password" value="Admin123!">
        </div>

        <button onclick="testLogin()">🔐 Tester la connexion</button>
        <button onclick="checkServerStatus()">🔍 Vérifier le serveur</button>

        <div id="result"></div>
    </div>

    <script>
        // Vérifier le status du serveur
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            const resultDiv = document.getElementById('result');
            
            statusElement.innerHTML = 'Vérification...';
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification du serveur...</p></div>';
            
            try {
                // Test simple de ping au serveur
                const response = await fetch('http://127.0.0.1:8000/api/', {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                    resultDiv.innerHTML = '<div class="success"><h4>✅ Serveur accessible</h4><p>Le serveur Django répond correctement.</p></div>';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                    resultDiv.innerHTML = `<div class="info"><h4>⚠️ Serveur accessible</h4><p>Status: ${response.status} ${response.statusText}</p></div>`;
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
                resultDiv.innerHTML = `<div class="error"><h4>❌ Erreur de connexion</h4><p>Impossible de joindre le serveur: ${error.message}</p><p><strong>Solutions:</strong></p><ul><li>Vérifier que le serveur Django est démarré</li><li>Vérifier l'URL: http://127.0.0.1:8000</li><li>Vérifier les paramètres CORS</li></ul></div>`;
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de connexion en cours...</p></div>';
            
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Connexion réussie !</h4>
                            <p><strong>Utilisateur:</strong> ${username}</p>
                            <p><strong>Token reçu:</strong> Oui</p>
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Échec de la connexion</h4>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Erreur:</strong> ${data.detail || data.error || 'Identifiants incorrects'}</p>
                            <h5>Réponse complète:</h5>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                            <p><strong>Vérifications:</strong></p>
                            <ul>
                                <li>Nom d'utilisateur: ${username}</li>
                                <li>Mot de passe: ${password}</li>
                                <li>Utilisateur existe-t-il dans la base de données ?</li>
                            </ul>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Erreur de connexion</h4>
                        <p><strong>Erreur:</strong> ${error.message}</p>
                        <p><strong>Solutions possibles:</strong></p>
                        <ul>
                            <li>Vérifier que le serveur Django est démarré sur le port 8000</li>
                            <li>Vérifier les paramètres CORS dans Django</li>
                            <li>Vérifier l'URL de l'API: http://127.0.0.1:8000/api/auth/token/</li>
                        </ul>
                    </div>
                `;
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

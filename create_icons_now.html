<!DOCTYPE html>
<html>
<head>
    <title>Créer Icônes PNG Maintenant</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 5px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #5a6fd8; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
    </style>
</head>
<body>
    <h1>🎨 Créateur d'Icônes PNG pour PWA</h1>
    <p>Cliquez sur "Créer Toutes les Icônes" pour générer toutes les tailles nécessaires :</p>
    
    <button onclick="createAllIcons()">🚀 Créer Toutes les Icônes</button>
    <button onclick="createIcon(192)">Créer 192x192</button>
    <button onclick="createIcon(144)">Créer 144x144</button>
    <button onclick="createIcon(96)">Créer 96x96</button>
    <button onclick="createIcon(72)">Créer 72x72</button>
    
    <div id="status" class="status">Prêt à créer les icônes...</div>
    
    <div id="canvases"></div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            canvas.style.display = 'block';
            canvas.style.margin = '10px';
            
            // Fond dégradé
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Coins arrondis (effet)
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.1;
            ctx.moveTo(radius, 0);
            ctx.lineTo(size - radius, 0);
            ctx.quadraticCurveTo(size, 0, size, radius);
            ctx.lineTo(size, size - radius);
            ctx.quadraticCurveTo(size, size, size - radius, size);
            ctx.lineTo(radius, size);
            ctx.quadraticCurveTo(0, size, 0, size - radius);
            ctx.lineTo(0, radius);
            ctx.quadraticCurveTo(0, 0, radius, 0);
            ctx.closePath();
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Cercle central blanc
            const centerX = size / 2;
            const centerY = size / 2;
            const circleRadius = size * 0.25;
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Texte "GPI"
            ctx.fillStyle = '#667eea';
            ctx.font = `bold ${size * 0.12}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('GPI', centerX, centerY);
            
            // Petits points décoratifs
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            const dotRadius = size * 0.015;
            
            for (let i = 0; i < 3; i++) {
                const x = centerX + (i - 1) * size * 0.08;
                const y = centerY - size * 0.15;
                ctx.beginPath();
                ctx.arc(x, y, dotRadius, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // Élément reconnaissance en bas
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY + size * 0.12, size * 0.06, size * 0.03, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Ajouter le canvas à la page pour visualisation
            const canvasContainer = document.getElementById('canvases');
            const wrapper = document.createElement('div');
            wrapper.style.display = 'inline-block';
            wrapper.style.margin = '10px';
            wrapper.style.textAlign = 'center';
            
            const label = document.createElement('div');
            label.textContent = `${size}x${size}`;
            label.style.marginBottom = '5px';
            label.style.fontWeight = 'bold';
            
            wrapper.appendChild(label);
            wrapper.appendChild(canvas);
            canvasContainer.appendChild(wrapper);
            
            // Télécharger automatiquement
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `icon-${size}x${size}.png`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
                
                updateStatus(`✅ Icône ${size}x${size}.png créée et téléchargée`);
            }, 'image/png', 1.0);
        }
        
        function createAllIcons() {
            const sizes = [72, 96, 144, 192, 384, 512];
            let index = 0;
            
            updateStatus('🚀 Création de toutes les icônes en cours...');
            
            // Vider le conteneur
            document.getElementById('canvases').innerHTML = '';
            
            function createNext() {
                if (index < sizes.length) {
                    createIcon(sizes[index]);
                    index++;
                    setTimeout(createNext, 800); // Délai entre chaque création
                } else {
                    updateStatus(`🎉 Toutes les icônes créées ! 
                    
📁 Instructions :
1. Les fichiers PNG ont été téléchargés
2. Placez-les dans le dossier : frontend/public/icons/
3. Redémarrez l'application React
4. Les erreurs d'icônes disparaîtront !`);
                }
            }
            
            createNext();
        }
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message.replace(/\n/g, '<br>');
        }
        
        // Message initial
        updateStatus(`📱 Prêt à créer les icônes PWA !
        
🎯 Objectif : Résoudre les erreurs d'icônes manquantes
📦 Tailles à créer : 72x72, 96x96, 144x144, 192x192, 384x384, 512x512
💾 Format : PNG haute qualité
🎨 Design : Logo GPI avec dégradé bleu`);
    </script>
</body>
</html>

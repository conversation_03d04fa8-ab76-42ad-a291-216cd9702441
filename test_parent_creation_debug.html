<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Création Parent</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Création Parent</h1>
        <p>Test complet du processus de création d'étudiant avec parent.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <!-- Formulaire de test -->
        <div class="test-section">
            <h3>📝 Formulaire de test</h3>
            <form id="test-form">
                <h4>Informations Étudiant</h4>
                <div class="form-group">
                    <label for="nom">Nom:</label>
                    <input type="text" id="nom" value="TestNom" required>
                </div>
                <div class="form-group">
                    <label for="prenom">Prénom:</label>
                    <input type="text" id="prenom" value="TestPrenom" required>
                </div>
                <div class="form-group">
                    <label for="date_naissance">Date de naissance:</label>
                    <input type="date" id="date_naissance" value="2010-01-01" required>
                </div>
                <div class="form-group">
                    <label for="sexe">Sexe:</label>
                    <select id="sexe">
                        <option value="M">Masculin</option>
                        <option value="F">Féminin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="classe">Classe ID:</label>
                    <input type="number" id="classe" value="1" required>
                </div>

                <h4>Informations Parent</h4>
                <div class="form-group">
                    <label for="parent_nom">Nom du parent:</label>
                    <input type="text" id="parent_nom" value="ParentNom">
                </div>
                <div class="form-group">
                    <label for="parent_prenom">Prénom du parent:</label>
                    <input type="text" id="parent_prenom" value="ParentPrenom">
                </div>
                <div class="form-group">
                    <label for="parent_telephone">Téléphone:</label>
                    <input type="text" id="parent_telephone" value="+33123456789">
                </div>
                <div class="form-group">
                    <label for="parent_email">Email:</label>
                    <input type="email" id="parent_email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="parent_relation">Relation:</label>
                    <select id="parent_relation">
                        <option value="pere">Père</option>
                        <option value="mere">Mère</option>
                        <option value="tuteur">Tuteur</option>
                    </select>
                </div>

                <button type="button" onclick="testCompleteProcess()">🧪 Tester le processus complet</button>
                <button type="button" onclick="testStudentOnly()">👤 Tester étudiant seulement</button>
                <button type="button" onclick="testParentOnly()">👨‍👩‍👧‍👦 Tester parent seulement</button>
            </form>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter et obtenir un token
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Fonction pour créer un étudiant
        async function createStudent(studentData) {
            const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(studentData)
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Erreur ${response.status}: ${errorData}`);
            }

            return await response.json();
        }

        // Fonction pour créer un parent
        async function createParent(parentData) {
            const response = await fetch('http://127.0.0.1:8000/api/parents/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(parentData)
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Erreur ${response.status}: ${errorData}`);
            }

            return await response.json();
        }

        // Fonction pour récupérer les parents d'un étudiant
        async function getStudentParents(studentId) {
            const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/parents/`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });

            if (!response.ok) {
                const errorData = await response.text();
                throw new Error(`Erreur ${response.status}: ${errorData}`);
            }

            return await response.json();
        }

        // Test du processus complet
        async function testCompleteProcess() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test du processus complet en cours...</p></div>';

            try {
                // 1. Authentification
                resultDiv.innerHTML += '<p>🔐 Authentification...</p>';
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // 2. Récupérer les données du formulaire
                const studentData = {
                    nom: document.getElementById('nom').value,
                    prenom: document.getElementById('prenom').value,
                    date_naissance: document.getElementById('date_naissance').value,
                    sexe: document.getElementById('sexe').value,
                    statut: 'actif',
                    classe: parseInt(document.getElementById('classe').value)
                };

                const parentData = {
                    nom: document.getElementById('parent_nom').value,
                    prenom: document.getElementById('parent_prenom').value,
                    telephone: document.getElementById('parent_telephone').value,
                    email: document.getElementById('parent_email').value,
                    relation: document.getElementById('parent_relation').value,
                    notifications_sms: true,
                    notifications_email: false
                };

                // 3. Créer l'étudiant
                resultDiv.innerHTML += '<p>👤 Création de l\'étudiant...</p>';
                const createdStudent = await createStudent(studentData);
                resultDiv.innerHTML += `<div class="success"><p>✅ Étudiant créé avec ID: ${createdStudent.id}</p></div>`;

                // 4. Créer le parent
                if (parentData.nom || parentData.prenom || parentData.telephone) {
                    resultDiv.innerHTML += '<p>👨‍👩‍👧‍👦 Création du parent...</p>';
                    parentData.etudiant = createdStudent.id;
                    
                    console.log('Données parent à envoyer:', parentData);
                    const createdParent = await createParent(parentData);
                    resultDiv.innerHTML += `<div class="success"><p>✅ Parent créé avec ID: ${createdParent.id}</p></div>`;

                    // 5. Vérifier la liaison
                    resultDiv.innerHTML += '<p>🔗 Vérification de la liaison...</p>';
                    const studentParents = await getStudentParents(createdStudent.id);
                    
                    if (studentParents.length > 0) {
                        resultDiv.innerHTML += `<div class="success"><p>✅ Liaison vérifiée: ${studentParents.length} parent(s) trouvé(s)</p></div>`;
                        resultDiv.innerHTML += `<pre>${JSON.stringify(studentParents, null, 2)}</pre>`;
                    } else {
                        resultDiv.innerHTML += `<div class="error"><p>❌ Aucun parent trouvé pour l'étudiant</p></div>`;
                    }
                } else {
                    resultDiv.innerHTML += '<div class="warning"><p>⚠️ Aucune information parent fournie</p></div>';
                }

                resultDiv.innerHTML += '<div class="success"><h3>🎉 Test terminé avec succès !</h3></div>';

            } catch (error) {
                console.error('Erreur:', error);
                resultDiv.innerHTML += `<div class="error"><h3>❌ Erreur</h3><p>${error.message}</p></div>`;
            }
        }

        // Test étudiant seulement
        async function testStudentOnly() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test création étudiant...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const studentData = {
                    nom: document.getElementById('nom').value,
                    prenom: document.getElementById('prenom').value,
                    date_naissance: document.getElementById('date_naissance').value,
                    sexe: document.getElementById('sexe').value,
                    statut: 'actif',
                    classe: parseInt(document.getElementById('classe').value)
                };

                const createdStudent = await createStudent(studentData);
                resultDiv.innerHTML += `<div class="success"><p>✅ Étudiant créé: ${JSON.stringify(createdStudent, null, 2)}</p></div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test parent seulement (nécessite un ID étudiant existant)
        async function testParentOnly() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test création parent...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const etudiantId = prompt('ID de l\'étudiant existant:');
                if (!etudiantId) {
                    throw new Error('ID étudiant requis');
                }

                const parentData = {
                    nom: document.getElementById('parent_nom').value,
                    prenom: document.getElementById('parent_prenom').value,
                    telephone: document.getElementById('parent_telephone').value,
                    email: document.getElementById('parent_email').value,
                    relation: document.getElementById('parent_relation').value,
                    notifications_sms: true,
                    notifications_email: false,
                    etudiant: parseInt(etudiantId)
                };

                const createdParent = await createParent(parentData);
                resultDiv.innerHTML += `<div class="success"><p>✅ Parent créé: ${JSON.stringify(createdParent, null, 2)}</p></div>`;

            } catch (error) {
                resultDiv.innerHTML += `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

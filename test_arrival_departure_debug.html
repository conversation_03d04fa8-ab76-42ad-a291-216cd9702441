<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Arrivée/Départ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-arrival {
            background-color: #28a745;
        }
        .btn-departure {
            background-color: #fd7e14;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .presence-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Système Arrivée/Départ</h1>
        <p>Diagnostic du problème de tracking des départs.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Tests de reconnaissance</h3>
            <p>Tester la reconnaissance faciale avec différents modes :</p>
            <button class="btn-arrival" onclick="testRecognition('arrivee')">🟢 Tester Arrivée</button>
            <button class="btn-departure" onclick="testRecognition('depart')">🟠 Tester Départ</button>
            <button onclick="checkTodayPresences()">📊 Vérifier présences du jour</button>
        </div>

        <div class="test-section">
            <h3>📋 Vérifications manuelles</h3>
            <button onclick="checkPresenceModel()">🗃️ Vérifier modèle Presence</button>
            <button onclick="testManualPresence()">✋ Créer présence manuelle</button>
            <button onclick="checkStudentPresences()">👤 Présences d'un étudiant</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Test de reconnaissance avec mode spécifique
        async function testRecognition(mode) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="info"><p>⏳ Test de reconnaissance en mode ${mode}...</p></div>`;

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Image de test (pixel rouge)
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
                
                console.log(`Envoi de la reconnaissance en mode: ${mode}`);
                
                const response = await fetch('http://127.0.0.1:8000/api/recognize_face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: mode
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test de reconnaissance ${mode} réussi</h3>
                            <p><strong>Mode envoyé:</strong> ${mode}</p>
                            <p><strong>Mode reçu:</strong> ${result.mode || 'Non défini'}</p>
                            <p><strong>Reconnu:</strong> ${result.recognized ? 'Oui' : 'Non'}</p>
                            ${result.recognized ? `
                                <p><strong>Étudiant:</strong> ${result.student ? result.student.prenom + ' ' + result.student.nom : 'Non trouvé'}</p>
                                <p><strong>Message:</strong> ${result.message || 'Aucun message'}</p>
                                <p><strong>Déjà présent:</strong> ${result.already_present ? 'Oui' : 'Non'}</p>
                                <p><strong>Heure:</strong> ${result.presence_time || 'Non définie'}</p>
                            ` : `
                                <p><strong>Raison:</strong> ${result.message || 'Visage non reconnu'}</p>
                            `}
                            <h4>Réponse complète:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur de reconnaissance</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les présences du jour
        async function checkTodayPresences() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des présences du jour...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/presences/today/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="success"><h3>📊 Présences du jour (${presences.length})</h3></div>`;
                    
                    if (presences.length === 0) {
                        html += '<div class="warning"><p>⚠️ Aucune présence enregistrée aujourd\'hui</p></div>';
                    } else {
                        presences.forEach(presence => {
                            const hasArrival = presence.heure_arrivee;
                            const hasDeparture = presence.heure_depart;
                            
                            html += `
                                <div class="presence-card">
                                    <h4>${presence.etudiant_nom} ${presence.etudiant_prenom}</h4>
                                    <p><strong>Date:</strong> ${presence.date}</p>
                                    <p><strong>Statut:</strong> ${presence.statut}</p>
                                    <p><strong>Arrivée:</strong> ${hasArrival ? `✅ ${presence.heure_arrivee}` : '❌ Non enregistrée'}</p>
                                    <p><strong>Départ:</strong> ${hasDeparture ? `✅ ${presence.heure_depart}` : '❌ Non enregistré'}</p>
                                    <p><strong>Notification envoyée:</strong> ${presence.notification_envoyee ? 'Oui' : 'Non'}</p>
                                    ${presence.commentaire ? `<p><strong>Commentaire:</strong> ${presence.commentaire}</p>` : ''}
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le modèle Presence
        async function checkPresenceModel() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification du modèle Presence...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="info"><h3>🗃️ Modèle Presence (${presences.length} enregistrements)</h3></div>`;
                    
                    // Analyser les données
                    let withArrival = 0;
                    let withDeparture = 0;
                    let withBoth = 0;
                    
                    presences.forEach(presence => {
                        if (presence.heure_arrivee) withArrival++;
                        if (presence.heure_depart) withDeparture++;
                        if (presence.heure_arrivee && presence.heure_depart) withBoth++;
                    });
                    
                    html += `
                        <div class="success">
                            <h4>📈 Statistiques</h4>
                            <p><strong>Total présences:</strong> ${presences.length}</p>
                            <p><strong>Avec arrivée:</strong> ${withArrival}</p>
                            <p><strong>Avec départ:</strong> ${withDeparture}</p>
                            <p><strong>Avec arrivée ET départ:</strong> ${withBoth}</p>
                        </div>
                    `;
                    
                    // Afficher les 5 dernières présences
                    html += '<h4>🕒 Dernières présences</h4>';
                    presences.slice(0, 5).forEach(presence => {
                        html += `
                            <div class="presence-card">
                                <p><strong>Étudiant:</strong> ${presence.etudiant_nom || 'ID ' + presence.etudiant}</p>
                                <p><strong>Date:</strong> ${presence.date}</p>
                                <p><strong>Arrivée:</strong> ${presence.heure_arrivee || 'Non enregistrée'}</p>
                                <p><strong>Départ:</strong> ${presence.heure_depart || 'Non enregistré'}</p>
                                <p><strong>Statut:</strong> ${presence.statut}</p>
                            </div>
                        `;
                    });
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Créer une présence manuelle pour test
        async function testManualPresence() {
            const resultDiv = document.getElementById('result');
            
            const studentId = prompt('ID de l\'étudiant pour créer une présence manuelle:');
            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Test annulé</p></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info"><p>⏳ Création de présence manuelle...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const now = new Date();
                const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS
                const currentDate = now.toISOString().slice(0, 10); // YYYY-MM-DD

                const presenceData = {
                    etudiant: parseInt(studentId),
                    date: currentDate,
                    heure_arrivee: currentTime,
                    heure_depart: null, // Sera rempli plus tard
                    statut: 'present'
                };

                const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(presenceData)
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Présence manuelle créée</h3>
                            <p><strong>ID:</strong> ${result.id}</p>
                            <p><strong>Étudiant:</strong> ${result.etudiant}</p>
                            <p><strong>Date:</strong> ${result.date}</p>
                            <p><strong>Arrivée:</strong> ${result.heure_arrivee}</p>
                            <p><strong>Départ:</strong> ${result.heure_depart || 'Non défini'}</p>
                            <h4>Réponse complète:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur de création</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les présences d'un étudiant spécifique
        async function checkStudentPresences() {
            const resultDiv = document.getElementById('result');
            
            const studentId = prompt('ID de l\'étudiant à vérifier:');
            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Vérification annulée</p></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des présences de l\'étudiant...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/presences/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="success"><h3>👤 Présences de l'étudiant ID ${studentId} (${presences.length})</h3></div>`;
                    
                    if (presences.length === 0) {
                        html += '<div class="warning"><p>⚠️ Aucune présence trouvée pour cet étudiant</p></div>';
                    } else {
                        presences.forEach(presence => {
                            html += `
                                <div class="presence-card">
                                    <p><strong>Date:</strong> ${presence.date}</p>
                                    <p><strong>Arrivée:</strong> ${presence.heure_arrivee || '❌ Non enregistrée'}</p>
                                    <p><strong>Départ:</strong> ${presence.heure_depart || '❌ Non enregistré'}</p>
                                    <p><strong>Statut:</strong> ${presence.statut}</p>
                                    <p><strong>Notification:</strong> ${presence.notification_envoyee ? 'Envoyée' : 'Non envoyée'}</p>
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

// Script pour créer des icônes PNG à partir du SVG
const fs = require('fs');
const path = require('path');

// Icône SVG simple en base64
const svgIcon = `
<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="192" height="192" rx="20" fill="url(#grad1)"/>
  <circle cx="96" cy="96" r="50" fill="white"/>
  <text x="96" y="106" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#667eea">GPI</text>
</svg>
`;

// Convertir SVG en base64
const svgBase64 = Buffer.from(svgIcon).toString('base64');
const dataUrl = `data:image/svg+xml;base64,${svgBase64}`;

console.log('Icône SVG créée en base64');
console.log('Pour créer les PNG, utilisez un convertisseur en ligne ou le fichier HTML fourni');
console.log('Data URL:', dataUrl);

// Créer un fichier HTML pour convertir
const htmlConverter = `
<!DOCTYPE html>
<html>
<head>
    <title>Convertisseur SVG vers PNG</title>
</head>
<body>
    <h1>Convertisseur d'Icônes</h1>
    <canvas id="canvas" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="convertToPNG(192)">Créer 192x192</button>
    <button onclick="convertToPNG(144)">Créer 144x144</button>
    <button onclick="convertToPNG(96)">Créer 96x96</button>
    <button onclick="convertToPNG(72)">Créer 72x72</button>
    
    <script>
        const svgData = \`${svgIcon}\`;
        
        function convertToPNG(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = \`icon-\${size}x\${size}.png\`;
                    link.click();
                    URL.revokeObjectURL(url);
                }, 'image/png');
            };
            
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            img.src = url;
        }
    </script>
</body>
</html>
`;

fs.writeFileSync(path.join(__dirname, 'converter.html'), htmlConverter);
console.log('Fichier converter.html créé');

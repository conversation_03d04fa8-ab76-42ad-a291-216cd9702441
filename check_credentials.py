#!/usr/bin/env python3
"""
Script pour vérifier et afficher les identifiants de connexion
"""

import os
import sys
import django

def check_credentials():
    """Vérifie les identifiants dans la base de données"""
    
    print("🔍 Vérification des identifiants de connexion")
    print("=" * 50)
    
    try:
        # Changer vers le répertoire backend
        backend_path = os.path.join(os.getcwd(), 'backend')
        if os.path.exists(backend_path):
            os.chdir(backend_path)
            print(f"📁 Répertoire de travail : {os.getcwd()}")
        
        # Configurer Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gestion_presence.settings')
        django.setup()
        
        # Importer le modèle utilisateur
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        print("\n📊 Utilisateurs dans la base de données :")
        print("-" * 40)
        
        users = User.objects.all()
        
        if not users.exists():
            print("❌ Aucun utilisateur trouvé dans la base de données")
            print("\n💡 Solutions :")
            print("1. Exécuter : python create_user.py")
            print("2. Exécuter : python manage.py createsuperuser")
            print("3. Exécuter : python ../create_admin.py")
            return False
        
        for user in users:
            print(f"\n👤 Utilisateur : {user.username}")
            print(f"   📧 Email : {user.email}")
            print(f"   🔑 Rôle : {user.role if hasattr(user, 'role') else 'N/A'}")
            print(f"   👑 Superuser : {'Oui' if user.is_superuser else 'Non'}")
            print(f"   ✅ Actif : {'Oui' if user.is_active else 'Non'}")
            print(f"   📅 Dernière connexion : {user.last_login or 'Jamais'}")
        
        print("\n🔑 Identifiants par défaut recommandés :")
        print("-" * 40)
        print("🌐 Interface d'administration Django :")
        print("   URL : http://localhost:8000/admin/")
        print("   Nom d'utilisateur : admin")
        print("   Mot de passe : admin123")
        
        print("\n🎨 Application frontend React :")
        print("   URL : http://localhost:5173")
        print("   Nom d'utilisateur : admin")
        print("   Mot de passe : Admin123!")
        
        # Vérifier si l'utilisateur admin existe
        admin_user = users.filter(username='admin').first()
        if admin_user:
            print(f"\n✅ Utilisateur 'admin' trouvé (ID: {admin_user.id})")
            
            # Tenter de réinitialiser le mot de passe
            try:
                admin_user.set_password('Admin123!')
                admin_user.save()
                print("🔄 Mot de passe réinitialisé à 'Admin123!'")
            except Exception as e:
                print(f"⚠️  Erreur lors de la réinitialisation : {e}")
        else:
            print("\n⚠️  Utilisateur 'admin' non trouvé")
            
            # Créer l'utilisateur admin
            try:
                admin_user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='Admin123!'
                )
                if hasattr(admin_user, 'role'):
                    admin_user.role = 'admin'
                    admin_user.save()
                print("✅ Utilisateur 'admin' créé avec succès !")
                print("   Nom d'utilisateur : admin")
                print("   Mot de passe : Admin123!")
            except Exception as e:
                print(f"❌ Erreur lors de la création : {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        print("\n💡 Solutions :")
        print("1. Vérifier que Django est configuré")
        print("2. Exécuter les migrations : python manage.py migrate")
        print("3. Vérifier la configuration de la base de données")
        return False

def show_connection_info():
    """Affiche les informations de connexion"""
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES IDENTIFIANTS DE CONNEXION")
    print("=" * 60)
    
    print("\n🔐 IDENTIFIANTS PRINCIPAUX :")
    print("┌─────────────────────────────────────────────────────┐")
    print("│  Nom d'utilisateur : admin                         │")
    print("│  Mot de passe      : Admin123!                     │")
    print("└─────────────────────────────────────────────────────┘")
    
    print("\n🌐 URLS D'ACCÈS :")
    print("• Interface d'administration : http://localhost:8000/admin/")
    print("• Application frontend       : http://localhost:5173")
    print("• API Backend               : http://localhost:8000/api/")
    
    print("\n📝 NOTES :")
    print("• Si les identifiants ne fonctionnent pas, exécutez ce script")
    print("• Le script réinitialise automatiquement le mot de passe")
    print("• Pour créer un nouvel utilisateur : python manage.py createsuperuser")

if __name__ == "__main__":
    print("🚀 Vérification des identifiants de connexion")
    
    success = check_credentials()
    show_connection_info()
    
    if success:
        print("\n✅ Vérification terminée avec succès !")
    else:
        print("\n❌ Vérification échouée. Consultez les messages d'erreur ci-dessus.")
    
    input("\nAppuyez sur Entrée pour continuer...")

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Visualisation Étudiant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .feature-box {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .feature-box h4 {
            margin-top: 0;
            color: #007bff;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .action-button {
            display: inline-flex;
            align-items: center;
            padding: 8px 12px;
            margin: 4px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s;
        }
        .btn-view {
            background: #d4f6d4;
            color: #0f5132;
            border: 1px solid #badbcc;
        }
        .btn-edit {
            background: #cce7ff;
            color: #084298;
            border: 1px solid #9ec5fe;
        }
        .btn-delete {
            background: #f8d7da;
            color: #842029;
            border: 1px solid #f5c2c7;
        }
        .modal-preview {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .student-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .info-label {
            font-size: 12px;
            color: #6c757d;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 4px;
        }
        .info-value {
            font-size: 14px;
            color: #212529;
            font-weight: 500;
        }
        .parent-card {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .no-parent {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            background: #f8f9fa;
            border-radius: 6px;
            border: 2px dashed #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👁️ Test Modal Visualisation Étudiant</h1>
        <p>Vérification de la nouvelle fonctionnalité "Voir" dans la page Students.</p>
        
        <div class="success">
            <h3>✅ Fonctionnalité implémentée</h3>
            <p>Ajout d'un bouton "Voir" dans la colonne Actions pour visualiser les détails complets de l'étudiant et de son parent.</p>
        </div>

        <h2>🎯 Nouvelles fonctionnalités</h2>

        <div class="feature-box">
            <h4>1. Bouton "Voir" dans les actions</h4>
            <p>Nouveau bouton avec icône œil (Eye) dans la colonne Actions du tableau des étudiants.</p>
            <div style="display: flex; gap: 10px; margin: 10px 0;">
                <span class="action-button btn-view">👁️ Voir</span>
                <span class="action-button btn-edit">✏️ Modifier</span>
                <span class="action-button btn-delete">🗑️ Supprimer</span>
            </div>
        </div>

        <div class="feature-box">
            <h4>2. Modal de visualisation détaillée</h4>
            <p>Modal responsive avec deux sections principales :</p>
            <ul>
                <li><strong>Informations de l'étudiant</strong> : Photo, nom, prénom, classe, statut, etc.</li>
                <li><strong>Informations des parents</strong> : Tous les parents enregistrés avec leurs détails</li>
            </ul>
        </div>

        <div class="feature-box">
            <h4>3. Gestion des cas d'absence de parent</h4>
            <p>Affichage d'un message informatif si aucun parent n'est enregistré pour l'étudiant.</p>
        </div>

        <h2>🖼️ Aperçu du modal</h2>

        <div class="modal-preview">
            <div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #dee2e6; padding-bottom: 15px; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #212529;">👁️ Détails de l'étudiant</h3>
                <span style="color: #6c757d; cursor: pointer;">✕</span>
            </div>

            <!-- Section Étudiant -->
            <div style="background: #f8f9fa; border-radius: 6px; padding: 15px; margin-bottom: 20px;">
                <h4 style="color: #007bff; margin-bottom: 15px;">👤 Informations de l'étudiant</h4>
                
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background: #007bff; color: white; display: inline-flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold;">
                        JD
                    </div>
                </div>

                <div class="student-info">
                    <div class="info-item">
                        <div class="info-label">Nom</div>
                        <div class="info-value">Dupont</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Prénom</div>
                        <div class="info-value">Jean</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Date de naissance</div>
                        <div class="info-value">15/03/2010</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Sexe</div>
                        <div class="info-value">Masculin</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Classe</div>
                        <div class="info-value">6ème A</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Statut</div>
                        <div class="info-value">
                            <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Actif</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Parents -->
            <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
                <h4 style="color: #28a745; margin-bottom: 15px;">📞 Informations des parents</h4>
                
                <!-- Cas avec parent -->
                <div class="parent-card">
                    <div class="student-info">
                        <div class="info-item">
                            <div class="info-label">Nom</div>
                            <div class="info-value">Dupont</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Prénom</div>
                            <div class="info-value">Marie</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Téléphone</div>
                            <div class="info-value">+33 6 12 34 56 78</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Relation</div>
                            <div class="info-value">Mère</div>
                        </div>
                        <div class="info-item" style="grid-column: 1 / -1;">
                            <div class="info-label">Email</div>
                            <div class="info-value"><EMAIL></div>
                        </div>
                        <div class="info-item" style="grid-column: 1 / -1;">
                            <div class="info-label">Préférences de notification</div>
                            <div style="display: flex; gap: 10px; margin-top: 5px;">
                                <span style="background: #d4edda; color: #155724; padding: 2px 8px; border-radius: 12px; font-size: 12px;">SMS: Activé</span>
                                <span style="background: #f8d7da; color: #721c24; padding: 2px 8px; border-radius: 12px; font-size: 12px;">Email: Désactivé</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cas sans parent -->
                <div class="no-parent" style="display: none;">
                    <div style="font-size: 48px; margin-bottom: 15px;">⚠️</div>
                    <p style="margin: 0; font-weight: 500;">Aucun parent enregistré pour cet étudiant</p>
                    <p style="margin: 10px 0 0 0; font-size: 14px;">Les informations du parent n'ont pas été saisies lors de l'inscription ou n'ont pas été sauvegardées correctement.</p>
                </div>
            </div>

            <div style="text-align: right; margin-top: 20px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                <button style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Fermer</button>
            </div>
        </div>

        <h2>🔧 Fonctionnalités techniques</h2>

        <div class="info">
            <h3>📋 Nouvelles fonctions ajoutées</h3>
            <div class="code-block">
                <strong>handleViewStudent(student: Student)</strong><br>
                - Récupère les parents de l'étudiant via fetchStudentParents()<br>
                - Ouvre le modal de visualisation<br>
                - Gère les erreurs de récupération des parents
            </div>
        </div>

        <div class="info">
            <h3>🎨 Nouveaux états React</h3>
            <div class="code-block">
                <strong>showViewModal:</strong> boolean - Contrôle l'affichage du modal<br>
                <strong>currentStudentParents:</strong> Parent[] - Liste des parents de l'étudiant sélectionné
            </div>
        </div>

        <div class="info">
            <h3>🔗 Imports ajoutés</h3>
            <div class="code-block">
                <strong>Eye</strong> - Icône pour le bouton "Voir"<br>
                <strong>fetchStudentParents</strong> - Fonction pour récupérer les parents<br>
                <strong>Parent</strong> - Type TypeScript pour les parents
            </div>
        </div>

        <h2>🚀 Test de la fonctionnalité</h2>

        <div class="warning">
            <h3>📝 Étapes de test</h3>
            <ol>
                <li><strong>Aller sur la page /students</strong></li>
                <li><strong>Localiser un étudiant</strong> dans le tableau</li>
                <li><strong>Cliquer sur le bouton "Voir"</strong> (icône œil verte)</li>
                <li><strong>Vérifier l'affichage</strong> des informations de l'étudiant</li>
                <li><strong>Vérifier l'affichage</strong> des informations du parent (ou message d'absence)</li>
                <li><strong>Tester la fermeture</strong> du modal</li>
            </ol>
        </div>

        <div class="success">
            <h3>✅ Résultats attendus</h3>
            <ul>
                <li>Bouton "Voir" visible dans la colonne Actions</li>
                <li>Modal s'ouvre avec les détails de l'étudiant</li>
                <li>Section parent affiche les informations ou un message d'absence</li>
                <li>Design responsive et cohérent avec le thème sombre/clair</li>
                <li>Fermeture du modal fonctionnelle</li>
            </ul>
        </div>

        <div class="info">
            <h3>🎯 Objectif atteint</h3>
            <p>Cette fonctionnalité permet maintenant de <strong>visualiser facilement</strong> si un parent a été correctement enregistré lors de l'inscription d'un élève, et de voir tous les détails de l'étudiant et de ses parents dans une interface claire et organisée.</p>
        </div>
    </div>
</body>
</html>

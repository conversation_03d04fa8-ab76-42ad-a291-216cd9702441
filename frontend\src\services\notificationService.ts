import { api } from './api';

export interface NotificationSettings {
  notifications_sms: boolean;
  notifications_email: boolean;
  notifications_arrivee: boolean;
  notifications_depart: boolean;
}

export interface Parent {
  id: number;
  nom: string;
  prenom: string;
  telephone: string;
  email: string;
  relation: string;
  notifications_sms: boolean;
  notifications_email: boolean;
  notifications_arrivee: boolean;
  notifications_depart: boolean;
  etudiant: number;
}

export interface NotificationHistory {
  id: number;
  parent: number;
  etudiant: number;
  type: 'arrivee' | 'depart' | 'absence' | 'retard';
  method: 'sms' | 'email';
  message: string;
  sent_at: string;
  success: boolean;
  error_message?: string;
}

class NotificationService {
  /**
   * Met à jour les paramètres de notification d'un parent
   */
  async updateParentNotificationSettings(
    parentId: number, 
    settings: Partial<NotificationSettings>
  ): Promise<Parent> {
    const response = await api.patch(`/parents/${parentId}/`, settings);
    return response.data;
  }

  /**
   * <PERSON><PERSON>cupère les paramètres de notification d'un parent
   */
  async getParentNotificationSettings(parentId: number): Promise<Parent> {
    const response = await api.get(`/parents/${parentId}/`);
    return response.data;
  }

  /**
   * Récupère tous les parents d'un étudiant avec leurs paramètres
   */
  async getStudentParents(studentId: number): Promise<Parent[]> {
    const response = await api.get(`/etudiants/${studentId}/parents/`);
    return response.data;
  }

  /**
   * Teste l'envoi d'une notification SMS
   */
  async testSMSNotification(parentId: number, message: string): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    const response = await api.post(`/parents/${parentId}/send_sms/`, {
      message: message
    });
    return response.data;
  }

  /**
   * Teste l'envoi d'une notification Email
   */
  async testEmailNotification(
    parentId: number, 
    subject: string, 
    message: string
  ): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    const response = await api.post(`/parents/${parentId}/send_email/`, {
      subject: subject,
      message: message
    });
    return response.data;
  }

  /**
   * Récupère l'historique des notifications envoyées
   */
  async getNotificationHistory(
    parentId?: number,
    studentId?: number,
    type?: string,
    limit: number = 50
  ): Promise<NotificationHistory[]> {
    const params = new URLSearchParams();
    if (parentId) params.append('parent', parentId.toString());
    if (studentId) params.append('etudiant', studentId.toString());
    if (type) params.append('type', type);
    params.append('limit', limit.toString());

    const response = await api.get(`/notifications/history/?${params.toString()}`);
    return response.data;
  }

  /**
   * Envoie une notification manuelle d'arrivée
   */
  async sendManualArrivalNotification(
    studentId: number,
    parentId?: number
  ): Promise<{
    success: boolean;
    message: string;
    notifications_sent: number;
  }> {
    const response = await api.post(`/notifications/manual/arrival/`, {
      student_id: studentId,
      parent_id: parentId
    });
    return response.data;
  }

  /**
   * Envoie une notification manuelle de départ
   */
  async sendManualDepartureNotification(
    studentId: number,
    parentId?: number
  ): Promise<{
    success: boolean;
    message: string;
    notifications_sent: number;
  }> {
    const response = await api.post(`/notifications/manual/departure/`, {
      student_id: studentId,
      parent_id: parentId
    });
    return response.data;
  }

  /**
   * Récupère les statistiques des notifications
   */
  async getNotificationStats(
    startDate?: string,
    endDate?: string
  ): Promise<{
    total_sent: number;
    sms_sent: number;
    email_sent: number;
    arrival_notifications: number;
    departure_notifications: number;
    success_rate: number;
    by_day: Array<{
      date: string;
      count: number;
      success_count: number;
    }>;
  }> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const response = await api.get(`/notifications/stats/?${params.toString()}`);
    return response.data;
  }

  /**
   * Active/désactive les notifications automatiques globalement
   */
  async toggleAutomaticNotifications(enabled: boolean): Promise<{
    success: boolean;
    message: string;
    automatic_notifications_enabled: boolean;
  }> {
    const response = await api.post('/notifications/settings/automatic/', {
      enabled: enabled
    });
    return response.data;
  }

  /**
   * Récupère les paramètres globaux des notifications
   */
  async getGlobalNotificationSettings(): Promise<{
    automatic_notifications_enabled: boolean;
    sms_provider: string;
    email_provider: string;
    default_notifications_arrivee: boolean;
    default_notifications_depart: boolean;
  }> {
    const response = await api.get('/notifications/settings/');
    return response.data;
  }

  /**
   * Met à jour les paramètres globaux des notifications
   */
  async updateGlobalNotificationSettings(settings: {
    automatic_notifications_enabled?: boolean;
    default_notifications_arrivee?: boolean;
    default_notifications_depart?: boolean;
  }): Promise<{
    success: boolean;
    message: string;
    settings: any;
  }> {
    const response = await api.patch('/notifications/settings/', settings);
    return response.data;
  }

  /**
   * Formate un message de notification d'arrivée
   */
  formatArrivalMessage(studentName: string, time: string, date: string): string {
    return `Bonjour, nous vous informons que ${studentName} est arrivé(e) à l'école aujourd'hui (${date}) à ${time}.`;
  }

  /**
   * Formate un message de notification de départ
   */
  formatDepartureMessage(studentName: string, time: string, date: string): string {
    return `Bonjour, nous vous informons que ${studentName} a quitté l'école aujourd'hui (${date}) à ${time}.`;
  }

  /**
   * Valide les paramètres de notification
   */
  validateNotificationSettings(settings: Partial<NotificationSettings>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Si les notifications d'arrivée ou de départ sont activées,
    // au moins un moyen de communication doit être activé
    if (settings.notifications_arrivee || settings.notifications_depart) {
      if (!settings.notifications_sms && !settings.notifications_email) {
        errors.push('Au moins un moyen de notification (SMS ou Email) doit être activé');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const notificationService = new NotificationService();

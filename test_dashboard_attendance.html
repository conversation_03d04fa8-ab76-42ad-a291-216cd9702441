<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard - Présences avec Arrivée/Départ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .arrival-badge {
            background-color: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .departure-badge {
            background-color: #fff3cd;
            color: #856404;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .missing {
            color: #6c757d;
            font-style: italic;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Dashboard - Présences avec Arrivée/Départ</h1>
        <p>Test de la nouvelle fonctionnalité d'affichage des heures d'arrivée et de départ dans le dashboard.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Frontend:</strong> http://localhost:5174/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div>
            <h3>🧪 Tests</h3>
            <button onclick="testDashboardData()">📊 Tester données dashboard</button>
            <button onclick="testRecentPresences()">📋 Tester présences récentes</button>
            <button onclick="createTestPresences()">➕ Créer présences de test</button>
            <button onclick="openDashboard()">🌐 Ouvrir Dashboard</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Test des données du dashboard
        async function testDashboardData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test des données du dashboard...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Récupérer les présences récentes
                const response = await fetch('http://127.0.0.1:8000/api/presences/today/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="success"><h3>✅ Données récupérées (${presences.length} présences)</h3></div>`;
                    
                    if (presences.length > 0) {
                        html += `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Étudiant</th>
                                        <th>Classe</th>
                                        <th>Date</th>
                                        <th>Arrivée</th>
                                        <th>Départ</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        presences.slice(0, 10).forEach(presence => {
                            const studentName = `${presence.etudiant_prenom || ''} ${presence.etudiant_nom || ''}`.trim() || 'Inconnu';
                            const arrivalTime = presence.heure_arrivee;
                            const departureTime = presence.heure_depart;
                            
                            html += `
                                <tr>
                                    <td>${studentName}</td>
                                    <td>${presence.classe_nom || 'Inconnue'}</td>
                                    <td>${presence.date}</td>
                                    <td>
                                        ${arrivalTime ? 
                                            `<span class="arrival-badge">🟢 ${arrivalTime}</span>` : 
                                            '<span class="missing">-</span>'
                                        }
                                    </td>
                                    <td>
                                        ${departureTime ? 
                                            `<span class="departure-badge">🟠 ${departureTime}</span>` : 
                                            '<span class="missing">-</span>'
                                        }
                                    </td>
                                    <td>${presence.statut}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                        
                        // Compter les présences avec arrivée et départ
                        const withArrival = presences.filter(p => p.heure_arrivee).length;
                        const withDeparture = presences.filter(p => p.heure_depart).length;
                        
                        html += `
                            <div class="info">
                                <h4>📊 Statistiques</h4>
                                <p><strong>Présences avec arrivée:</strong> ${withArrival}/${presences.length}</p>
                                <p><strong>Présences avec départ:</strong> ${withDeparture}/${presences.length}</p>
                                <p><strong>Présences complètes (arrivée + départ):</strong> ${presences.filter(p => p.heure_arrivee && p.heure_depart).length}/${presences.length}</p>
                            </div>
                        `;
                    } else {
                        html += '<div class="info"><p>ℹ️ Aucune présence trouvée pour aujourd\'hui</p></div>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test des présences récentes (format dashboard)
        async function testRecentPresences() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test du format dashboard...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Simuler le format dashboard
                const response = await fetch('http://127.0.0.1:8000/api/presences/today/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    // Formater comme dans dashboardService.ts
                    const recentAttendance = presences.map(presence => {
                        const studentName = `${presence.etudiant_prenom || ''} ${presence.etudiant_nom || ''}`.trim() || 'Inconnu';
                        const formattedTimestamp = presence.heure_arrivee
                            ? `${presence.date} ${presence.heure_arrivee}`
                            : presence.date;

                        let status = 'present';
                        if (presence.statut === 'absent') status = 'absent';
                        if (presence.statut === 'retard') status = 'late';

                        return {
                            id: presence.id,
                            name: studentName,
                            class: presence.classe_nom || 'Inconnue',
                            timestamp: formattedTimestamp,
                            arrivalTime: presence.heure_arrivee || undefined,
                            departureTime: presence.heure_depart || undefined,
                            date: presence.date,
                            status: status,
                            avatar: presence.etudiant_photo || null
                        };
                    });

                    let html = `<div class="success"><h3>✅ Format Dashboard (${recentAttendance.length} entrées)</h3></div>`;
                    
                    if (recentAttendance.length > 0) {
                        html += '<h4>🔍 Aperçu des données formatées :</h4>';
                        html += `<pre>${JSON.stringify(recentAttendance.slice(0, 3), null, 2)}</pre>`;
                        
                        html += `
                            <table>
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Classe</th>
                                        <th>Date</th>
                                        <th>Arrivée</th>
                                        <th>Départ</th>
                                        <th>Statut</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;
                        
                        recentAttendance.slice(0, 5).forEach(attendance => {
                            html += `
                                <tr>
                                    <td>${attendance.name}</td>
                                    <td>${attendance.class}</td>
                                    <td>${attendance.date}</td>
                                    <td>
                                        ${attendance.arrivalTime ? 
                                            `<span class="arrival-badge">🟢 ${attendance.arrivalTime}</span>` : 
                                            '<span class="missing">-</span>'
                                        }
                                    </td>
                                    <td>
                                        ${attendance.departureTime ? 
                                            `<span class="departure-badge">🟠 ${attendance.departureTime}</span>` : 
                                            '<span class="missing">-</span>'
                                        }
                                    </td>
                                    <td>${attendance.status}</td>
                                </tr>
                            `;
                        });
                        
                        html += '</tbody></table>';
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Créer des présences de test avec arrivée et départ
        async function createTestPresences() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Création de présences de test...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Récupérer les étudiants
                const studentsResponse = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!studentsResponse.ok) {
                    throw new Error('Impossible de récupérer les étudiants');
                }

                const students = await studentsResponse.json();
                
                if (students.length === 0) {
                    resultDiv.innerHTML = '<div class="error"><p>❌ Aucun étudiant trouvé</p></div>';
                    return;
                }

                const now = new Date();
                const currentDate = now.toISOString().slice(0, 10);
                const arrivalTime = '08:30:00';
                const departureTime = '16:45:00';

                let created = 0;
                let updated = 0;

                // Créer/mettre à jour des présences pour les 3 premiers étudiants
                for (let i = 0; i < Math.min(3, students.length); i++) {
                    const student = students[i];
                    
                    const presenceData = {
                        etudiant: student.id,
                        date: currentDate,
                        heure_arrivee: arrivalTime,
                        heure_depart: i < 2 ? departureTime : null, // Seuls les 2 premiers ont un départ
                        statut: 'present'
                    };

                    const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: JSON.stringify(presenceData)
                    });

                    if (response.ok) {
                        created++;
                    } else {
                        // Essayer de mettre à jour si elle existe déjà
                        const existingResponse = await fetch(`http://127.0.0.1:8000/api/presences/?etudiant=${student.id}&date=${currentDate}`, {
                            headers: {
                                'Authorization': `Bearer ${authToken}`
                            }
                        });

                        if (existingResponse.ok) {
                            const existingPresences = await existingResponse.json();
                            if (existingPresences.length > 0) {
                                const updateResponse = await fetch(`http://127.0.0.1:8000/api/presences/${existingPresences[0].id}/`, {
                                    method: 'PATCH',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    },
                                    body: JSON.stringify({
                                        heure_depart: i < 2 ? departureTime : null
                                    })
                                });

                                if (updateResponse.ok) {
                                    updated++;
                                }
                            }
                        }
                    }
                }

                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Présences de test créées</h3>
                        <p><strong>Créées:</strong> ${created}</p>
                        <p><strong>Mises à jour:</strong> ${updated}</p>
                        <p>Vous pouvez maintenant tester le dashboard !</p>
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Ouvrir le dashboard
        function openDashboard() {
            window.open('http://localhost:5174/dashboard', '_blank');
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Backend actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Backend répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Backend inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Initialisation
        checkServerStatus();
    </script>
</body>
</html>

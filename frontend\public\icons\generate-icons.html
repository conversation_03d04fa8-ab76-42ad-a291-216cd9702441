<!DOCTYPE html>
<html>
<head>
    <title>Générateur d'Icônes PWA</title>
</head>
<body>
    <h1>Générateur d'Icônes PWA</h1>
    <p>Ce script génère des icônes basiques pour la PWA</p>
    
    <canvas id="canvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    
    <button onclick="generateIcon(72)">Générer 72x72</button>
    <button onclick="generateIcon(96)">Générer 96x96</button>
    <button onclick="generateIcon(128)">Générer 128x128</button>
    <button onclick="generateIcon(144)">Générer 144x144</button>
    <button onclick="generateIcon(152)">Générer 152x152</button>
    <button onclick="generateIcon(192)">Générer 192x192</button>
    <button onclick="generateIcon(384)">Générer 384x384</button>
    <button onclick="generateIcon(512)">Générer 512x512</button>
    
    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Redimensionner le canvas
            canvas.width = size;
            canvas.height = size;
            
            // Fond dégradé
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Icône centrale (cercle blanc avec "GPI")
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.3;
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Texte "GPI"
            ctx.fillStyle = '#667eea';
            ctx.font = `bold ${size * 0.15}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('GPI', centerX, centerY);
            
            // Télécharger l'image
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Générer l'icône par défaut
        generateIcon(192);
    </script>
</body>
</html>

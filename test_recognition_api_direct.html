<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Reconnaissance Direct</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-arrival {
            background-color: #28a745;
        }
        .btn-departure {
            background-color: #fd7e14;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .step {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test API Reconnaissance Direct</h1>
        <p>Test direct de l'API de reconnaissance faciale pour diagnostiquer le problème de départ.</p>
        
        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <div class="test-section">
            <h3>🎯 Test avec étudiant existant</h3>
            <p>Sélectionnez un étudiant existant et testez la reconnaissance :</p>
            
            <div class="form-group">
                <label for="student-select">Étudiant :</label>
                <select id="student-select">
                    <option value="">Chargement...</option>
                </select>
                <button onclick="loadStudents()">🔄 Recharger</button>
            </div>

            <div class="form-group">
                <label for="mode-select">Mode :</label>
                <select id="mode-select">
                    <option value="arrivee">Arrivée</option>
                    <option value="depart">Départ</option>
                </select>
            </div>

            <button onclick="testWithSelectedStudent()" class="btn-arrival">🧪 Tester reconnaissance</button>
            <button onclick="checkStudentPresences()" class="btn-departure">📊 Voir présences</button>
        </div>

        <div class="test-section">
            <h3>🔧 Tests manuels</h3>
            <button onclick="testRecognitionRaw()">🔍 Test reconnaissance brute</button>
            <button onclick="createManualPresence()">✋ Créer présence manuelle</button>
            <button onclick="checkAllPresencesToday()">📋 Présences du jour</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;
        let students = [];

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Charger la liste des étudiants
        async function loadStudents() {
            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    students = await response.json();
                    const select = document.getElementById('student-select');
                    select.innerHTML = '<option value="">Sélectionnez un étudiant</option>';
                    
                    students.forEach(student => {
                        const option = document.createElement('option');
                        option.value = student.id;
                        option.textContent = `${student.prenom} ${student.nom} (ID: ${student.id})`;
                        select.appendChild(option);
                    });
                } else {
                    throw new Error(`Erreur ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                console.error('Erreur lors du chargement des étudiants:', error);
                document.getElementById('student-select').innerHTML = '<option value="">Erreur de chargement</option>';
            }
        }

        // Test avec l'étudiant sélectionné
        async function testWithSelectedStudent() {
            const resultDiv = document.getElementById('result');
            const studentId = document.getElementById('student-select').value;
            const mode = document.getElementById('mode-select').value;

            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Veuillez sélectionner un étudiant</p></div>';
                return;
            }

            resultDiv.innerHTML = `<div class="info"><p>⏳ Test de reconnaissance pour l'étudiant ID ${studentId} en mode ${mode}...</p></div>`;

            try {
                // Image de test (carré rouge 10x10)
                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                console.log(`Test reconnaissance - Étudiant: ${studentId}, Mode: ${mode}`);
                
                const response = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: mode
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    const student = students.find(s => s.id == studentId);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Test de reconnaissance réussi</h3>
                            <p><strong>Étudiant testé:</strong> ${student ? student.prenom + ' ' + student.nom : 'ID ' + studentId}</p>
                            <p><strong>Mode envoyé:</strong> ${mode}</p>
                            <p><strong>Mode reçu:</strong> ${result.mode || 'Non défini'}</p>
                            <p><strong>Reconnu:</strong> ${result.recognized ? '✅ Oui' : '❌ Non'}</p>
                            ${result.recognized ? `
                                <p><strong>Étudiant reconnu:</strong> ${result.student ? result.student.prenom + ' ' + result.student.nom : 'Non trouvé'}</p>
                                <p><strong>Confiance:</strong> ${result.confidence ? result.confidence.toFixed(2) + '%' : 'Non définie'}</p>
                                <p><strong>Message:</strong> ${result.message}</p>
                                <p><strong>Déjà présent:</strong> ${result.already_present ? 'Oui' : 'Non'}</p>
                                <p><strong>Heure:</strong> ${result.presence_time || 'Non définie'}</p>
                            ` : `
                                <p><strong>Raison:</strong> ${result.message}</p>
                            `}
                            <h4>Réponse complète:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur de reconnaissance</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier les présences de l'étudiant sélectionné
        async function checkStudentPresences() {
            const resultDiv = document.getElementById('result');
            const studentId = document.getElementById('student-select').value;

            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Veuillez sélectionner un étudiant</p></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des présences...</p></div>';

            try {
                const response = await fetch(`http://127.0.0.1:8000/api/etudiants/${studentId}/presences/`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    const student = students.find(s => s.id == studentId);
                    
                    let html = `<div class="success"><h3>📊 Présences de ${student ? student.prenom + ' ' + student.nom : 'ID ' + studentId} (${presences.length})</h3></div>`;
                    
                    if (presences.length === 0) {
                        html += '<div class="warning"><p>⚠️ Aucune présence trouvée pour cet étudiant</p></div>';
                    } else {
                        presences.slice(0, 5).forEach(presence => {
                            const hasArrival = presence.heure_arrivee;
                            const hasDeparture = presence.heure_depart;
                            
                            html += `
                                <div class="step">
                                    <h4>📅 ${presence.date}</h4>
                                    <p><strong>Arrivée:</strong> ${hasArrival ? `✅ ${presence.heure_arrivee}` : '❌ Non enregistrée'}</p>
                                    <p><strong>Départ:</strong> ${hasDeparture ? `✅ ${presence.heure_depart}` : '❌ Non enregistré'}</p>
                                    <p><strong>Statut:</strong> ${presence.statut}</p>
                                    <p><strong>Notification:</strong> ${presence.notification_envoyee ? 'Envoyée' : 'Non envoyée'}</p>
                                    ${presence.commentaire ? `<p><strong>Commentaire:</strong> ${presence.commentaire}</p>` : ''}
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test de reconnaissance brute
        async function testRecognitionRaw() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de reconnaissance brute...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAFUlEQVR42mP8/5+hnoEIwDiqkL4KAcT9GO0U4BxoAAAAAElFTkSuQmCC';
                
                // Test arrivée
                const arrivalResponse = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'arrivee'
                    })
                });

                const arrivalResult = await arrivalResponse.json();

                // Test départ
                const departureResponse = await fetch('http://127.0.0.1:8000/api/reconnaissance/face/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        image: testImageBase64,
                        mode: 'depart'
                    })
                });

                const departureResult = await departureResponse.json();

                resultDiv.innerHTML = `
                    <div class="info">
                        <h3>🔍 Test reconnaissance brute</h3>
                        
                        <h4>🟢 Test Arrivée</h4>
                        <p><strong>Status:</strong> ${arrivalResponse.status}</p>
                        <p><strong>Reconnu:</strong> ${arrivalResult.recognized ? 'Oui' : 'Non'}</p>
                        <p><strong>Mode:</strong> ${arrivalResult.mode}</p>
                        <p><strong>Message:</strong> ${arrivalResult.message}</p>
                        <pre>${JSON.stringify(arrivalResult, null, 2)}</pre>
                        
                        <h4>🟠 Test Départ</h4>
                        <p><strong>Status:</strong> ${departureResponse.status}</p>
                        <p><strong>Reconnu:</strong> ${departureResult.recognized ? 'Oui' : 'Non'}</p>
                        <p><strong>Mode:</strong> ${departureResult.mode}</p>
                        <p><strong>Message:</strong> ${departureResult.message}</p>
                        <pre>${JSON.stringify(departureResult, null, 2)}</pre>
                    </div>
                `;

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Créer une présence manuelle
        async function createManualPresence() {
            const resultDiv = document.getElementById('result');
            const studentId = document.getElementById('student-select').value;

            if (!studentId) {
                resultDiv.innerHTML = '<div class="warning"><p>⚠️ Veuillez sélectionner un étudiant</p></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info"><p>⏳ Création de présence manuelle...</p></div>';

            try {
                const now = new Date();
                const currentTime = now.toTimeString().slice(0, 8);
                const currentDate = now.toISOString().slice(0, 10);

                const presenceData = {
                    etudiant: parseInt(studentId),
                    date: currentDate,
                    heure_arrivee: currentTime,
                    heure_depart: null,
                    statut: 'present'
                };

                const response = await fetch('http://127.0.0.1:8000/api/presences/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(presenceData)
                });

                if (response.ok) {
                    const result = await response.json();
                    const student = students.find(s => s.id == studentId);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Présence manuelle créée</h3>
                            <p><strong>Étudiant:</strong> ${student ? student.prenom + ' ' + student.nom : 'ID ' + studentId}</p>
                            <p><strong>Date:</strong> ${result.date}</p>
                            <p><strong>Arrivée:</strong> ${result.heure_arrivee}</p>
                            <p><strong>Départ:</strong> ${result.heure_depart || 'Non défini'}</p>
                            <h4>Réponse complète:</h4>
                            <pre>${JSON.stringify(result, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier toutes les présences du jour
        async function checkAllPresencesToday() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Vérification des présences du jour...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                const response = await fetch('http://127.0.0.1:8000/api/presences/today/', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const presences = await response.json();
                    
                    let html = `<div class="success"><h3>📊 Présences du jour (${presences.length})</h3></div>`;
                    
                    if (presences.length === 0) {
                        html += '<div class="warning"><p>⚠️ Aucune présence enregistrée aujourd\'hui</p></div>';
                    } else {
                        presences.forEach(presence => {
                            const hasArrival = presence.heure_arrivee;
                            const hasDeparture = presence.heure_depart;
                            
                            html += `
                                <div class="step">
                                    <h4>${presence.etudiant_nom || 'ID ' + presence.etudiant} ${presence.etudiant_prenom || ''}</h4>
                                    <p><strong>Date:</strong> ${presence.date}</p>
                                    <p><strong>Arrivée:</strong> ${hasArrival ? `✅ ${presence.heure_arrivee}` : '❌ Non enregistrée'}</p>
                                    <p><strong>Départ:</strong> ${hasDeparture ? `✅ ${presence.heure_depart}` : '❌ Non enregistré'}</p>
                                    <p><strong>Statut:</strong> ${presence.statut}</p>
                                </div>
                            `;
                        });
                    }
                    
                    resultDiv.innerHTML = html;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${errorData}</p></div>`;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Initialisation
        checkServerStatus();
        loadStudents();
    </script>
</body>
</html>

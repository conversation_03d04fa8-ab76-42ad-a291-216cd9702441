<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Création Parent - Corrigé</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .fix-highlight {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Création Parent - Corrigé</h1>
        <p>Vérification de la correction du problème de création des parents.</p>
        
        <div class="success">
            <h3>✅ Problème identifié et corrigé</h3>
            <p>Le problème était dans le mapping des valeurs de relation entre le frontend et le backend.</p>
        </div>

        <div class="fix-highlight">
            <h3>🔧 Correction appliquée</h3>
            <p><strong>Problème :</strong> Le frontend envoyait <code>'pere'</code>, <code>'mere'</code> mais le backend attendait <code>'père'</code>, <code>'mère'</code> (avec accents).</p>
            <p><strong>Solution :</strong> Ajout d'un mapping dans le frontend pour convertir les valeurs avant envoi.</p>
            <pre>const relationMapping = {
  'pere': 'père',
  'mere': 'mère', 
  'tuteur': 'tuteur',
  'autre': 'autre'
};</pre>
        </div>

        <div class="info">
            <h3>📋 Informations</h3>
            <p><strong>Backend API:</strong> http://127.0.0.1:8000/api/</p>
            <p><strong>Status serveur:</strong> <span id="server-status">Vérification...</span></p>
        </div>

        <button onclick="testParentCreation()">🧪 Tester la création parent</button>
        <button onclick="testCompleteFlow()">🔄 Tester le flux complet</button>

        <div id="result"></div>
    </div>

    <script>
        let authToken = null;

        // Fonction pour se connecter
        async function authenticate() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/auth/token/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'Admin123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access;
                    return true;
                } else {
                    console.error('Échec de l\'authentification');
                    return false;
                }
            } catch (error) {
                console.error('Erreur d\'authentification:', error);
                return false;
            }
        }

        // Test de création parent avec les bonnes valeurs
        async function testParentCreation() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test de création parent...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // Données parent avec les bonnes valeurs de relation
                const parentData = {
                    nom: 'TestParent',
                    prenom: 'Parent',
                    telephone: '+33123456789',
                    email: '<EMAIL>',
                    relation: 'père', // Avec accent comme attendu par le backend
                    notifications_sms: true,
                    notifications_email: false,
                    etudiant: 1 // Supposons qu'un étudiant avec ID 1 existe
                };

                console.log('Données parent à envoyer:', parentData);

                const response = await fetch('http://127.0.0.1:8000/api/parents/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(parentData)
                });

                if (response.ok) {
                    const createdParent = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Parent créé avec succès !</h3>
                            <p><strong>ID:</strong> ${createdParent.id}</p>
                            <p><strong>Nom:</strong> ${createdParent.nom} ${createdParent.prenom}</p>
                            <p><strong>Relation:</strong> ${createdParent.relation}</p>
                            <p><strong>Téléphone:</strong> ${createdParent.telephone}</p>
                            <h4>Réponse complète:</h4>
                            <pre>${JSON.stringify(createdParent, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    const errorData = await response.text();
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Échec de la création</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <p><strong>Erreur:</strong></p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Test du flux complet avec mapping
        async function testCompleteFlow() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info"><p>⏳ Test du flux complet avec mapping...</p></div>';

            try {
                const authenticated = await authenticate();
                if (!authenticated) {
                    throw new Error('Échec de l\'authentification');
                }

                // 1. Créer un étudiant
                const studentData = {
                    nom: 'TestEtudiant',
                    prenom: 'Etudiant',
                    date_naissance: '2010-01-01',
                    sexe: 'M',
                    statut: 'actif',
                    classe: 1
                };

                resultDiv.innerHTML += '<p>👤 Création de l\'étudiant...</p>';
                const studentResponse = await fetch('http://127.0.0.1:8000/api/etudiants/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(studentData)
                });

                if (!studentResponse.ok) {
                    throw new Error('Échec de la création de l\'étudiant');
                }

                const createdStudent = await studentResponse.json();
                resultDiv.innerHTML += `<div class="success"><p>✅ Étudiant créé avec ID: ${createdStudent.id}</p></div>`;

                // 2. Simuler le mapping frontend et créer le parent
                const frontendRelation = 'pere'; // Valeur du frontend
                const relationMapping = {
                    'pere': 'père',
                    'mere': 'mère',
                    'tuteur': 'tuteur',
                    'autre': 'autre'
                };

                const parentData = {
                    nom: 'TestParent',
                    prenom: 'Parent',
                    telephone: '+33123456789',
                    email: '<EMAIL>',
                    relation: relationMapping[frontendRelation], // Mapping appliqué
                    notifications_sms: true,
                    notifications_email: false,
                    etudiant: createdStudent.id
                };

                resultDiv.innerHTML += '<p>👨‍👩‍👧‍👦 Création du parent avec mapping...</p>';
                resultDiv.innerHTML += `<p><strong>Mapping:</strong> '${frontendRelation}' → '${parentData.relation}'</p>`;

                const parentResponse = await fetch('http://127.0.0.1:8000/api/parents/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(parentData)
                });

                if (parentResponse.ok) {
                    const createdParent = await parentResponse.json();
                    resultDiv.innerHTML += `
                        <div class="success">
                            <h3>🎉 Flux complet réussi !</h3>
                            <p><strong>Étudiant:</strong> ${createdStudent.nom} ${createdStudent.prenom} (ID: ${createdStudent.id})</p>
                            <p><strong>Parent:</strong> ${createdParent.nom} ${createdParent.prenom} (ID: ${createdParent.id})</p>
                            <p><strong>Relation:</strong> ${createdParent.relation}</p>
                            <p><strong>Liaison:</strong> Parent lié à l'étudiant ID ${createdParent.etudiant}</p>
                        </div>
                    `;
                } else {
                    const errorData = await parentResponse.text();
                    resultDiv.innerHTML += `
                        <div class="error">
                            <h3>❌ Échec de la création du parent</h3>
                            <p><strong>Status:</strong> ${parentResponse.status}</p>
                            <pre>${errorData}</pre>
                        </div>
                    `;
                }

            } catch (error) {
                resultDiv.innerHTML += `<div class="error"><p>❌ Erreur: ${error.message}</p></div>`;
            }
        }

        // Vérifier le status du serveur au chargement
        async function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            try {
                const response = await fetch('http://127.0.0.1:8000/api/');
                if (response.ok || response.status === 401) {
                    statusElement.innerHTML = '✅ Serveur actif';
                    statusElement.style.color = 'green';
                } else {
                    statusElement.innerHTML = `⚠️ Serveur répond (${response.status})`;
                    statusElement.style.color = 'orange';
                }
            } catch (error) {
                statusElement.innerHTML = '❌ Serveur inaccessible';
                statusElement.style.color = 'red';
            }
        }

        // Vérifier le status au chargement
        checkServerStatus();
    </script>
</body>
</html>

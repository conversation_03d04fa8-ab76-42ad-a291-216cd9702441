import { useState, useEffect } from 'react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  isUpdateAvailable: boolean;
  installPrompt: BeforeInstallPromptEvent | null;
}

interface PWAActions {
  installApp: () => Promise<void>;
  updateApp: () => Promise<void>;
  requestNotificationPermission: () => Promise<NotificationPermission>;
  showNotification: (title: string, options?: NotificationOptions) => Promise<void>;
  registerForPush: () => Promise<PushSubscription | null>;
}

export const usePWA = (): PWAState & PWAActions => {
  const [state, setState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    isUpdateAvailable: false,
    installPrompt: null,
  });

  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // Vérifier si l'app est déjà installée
    const checkIfInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isInstalled = isStandalone || isInWebAppiOS;
      
      setState(prev => ({ ...prev, isInstalled }));
    };

    // Enregistrer le Service Worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js');
          setServiceWorkerRegistration(registration);
          
          console.log('Service Worker enregistré:', registration);

          // Vérifier les mises à jour
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setState(prev => ({ ...prev, isUpdateAvailable: true }));
                }
              });
            }
          });

        } catch (error) {
          console.error('Erreur lors de l\'enregistrement du Service Worker:', error);
        }
      }
    };

    // Gérer l'événement beforeinstallprompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      const installPrompt = e as BeforeInstallPromptEvent;
      setState(prev => ({ 
        ...prev, 
        isInstallable: true, 
        installPrompt 
      }));
    };

    // Gérer les changements de statut en ligne/hors ligne
    const handleOnline = () => setState(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setState(prev => ({ ...prev, isOnline: false }));

    // Gérer l'installation de l'app
    const handleAppInstalled = () => {
      setState(prev => ({ 
        ...prev, 
        isInstalled: true, 
        isInstallable: false, 
        installPrompt: null 
      }));
    };

    // Ajouter les event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initialiser
    checkIfInstalled();
    registerServiceWorker();

    // Cleanup
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Installer l'application
  const installApp = async (): Promise<void> => {
    if (!state.installPrompt) {
      throw new Error('Aucune invite d\'installation disponible');
    }

    try {
      await state.installPrompt.prompt();
      const choiceResult = await state.installPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('Utilisateur a accepté l\'installation');
      } else {
        console.log('Utilisateur a refusé l\'installation');
      }

      setState(prev => ({ ...prev, installPrompt: null, isInstallable: false }));
    } catch (error) {
      console.error('Erreur lors de l\'installation:', error);
      throw error;
    }
  };

  // Mettre à jour l'application
  const updateApp = async (): Promise<void> => {
    if (!serviceWorkerRegistration) {
      throw new Error('Service Worker non disponible');
    }

    try {
      const newWorker = serviceWorkerRegistration.waiting;
      if (newWorker) {
        newWorker.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      throw error;
    }
  };

  // Demander la permission pour les notifications
  const requestNotificationPermission = async (): Promise<NotificationPermission> => {
    if (!('Notification' in window)) {
      throw new Error('Les notifications ne sont pas supportées');
    }

    const permission = await Notification.requestPermission();
    return permission;
  };

  // Afficher une notification
  const showNotification = async (title: string, options?: NotificationOptions): Promise<void> => {
    if (!serviceWorkerRegistration) {
      throw new Error('Service Worker non disponible');
    }

    if (Notification.permission !== 'granted') {
      throw new Error('Permission de notification non accordée');
    }

    const defaultOptions: NotificationOptions = {
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      ...options
    };

    await serviceWorkerRegistration.showNotification(title, defaultOptions);
  };

  // S'inscrire aux notifications push
  const registerForPush = async (): Promise<PushSubscription | null> => {
    if (!serviceWorkerRegistration) {
      throw new Error('Service Worker non disponible');
    }

    if (!('PushManager' in window)) {
      throw new Error('Push messaging non supporté');
    }

    try {
      const subscription = await serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.REACT_APP_VAPID_PUBLIC_KEY
      });

      console.log('Inscription push réussie:', subscription);
      return subscription;
    } catch (error) {
      console.error('Erreur lors de l\'inscription push:', error);
      return null;
    }
  };

  return {
    ...state,
    installApp,
    updateApp,
    requestNotificationPermission,
    showNotification,
    registerForPush,
  };
};

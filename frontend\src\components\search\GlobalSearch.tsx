import React, { useState, useEffect, useRef } from 'react';
import { MagnifyingGlassIcon, XMarkIcon, ClockIcon, UserIcon, AcademicCapIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { useNavigate } from 'react-router-dom';

interface SearchResult {
  id: string;
  type: 'student' | 'class' | 'presence' | 'parent' | 'analytics';
  title: string;
  subtitle: string;
  description?: string;
  url: string;
  icon: React.ReactNode;
  metadata?: {
    date?: string;
    status?: string;
    count?: number;
  };
}

interface SearchHistory {
  query: string;
  timestamp: Date;
  results: number;
}

const GlobalSearch: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<SearchHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Charger l'historique des recherches depuis localStorage
  useEffect(() => {
    const saved = localStorage.getItem('searchHistory');
    if (saved) {
      try {
        const parsed = JSON.parse(saved).map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        }));
        setRecentSearches(parsed);
      } catch (error) {
        console.error('Erreur lors du chargement de l\'historique:', error);
      }
    }
  }, []);

  // Sauvegarder l'historique des recherches
  const saveSearchHistory = (query: string, resultsCount: number) => {
    const newSearch: SearchHistory = {
      query,
      timestamp: new Date(),
      results: resultsCount
    };

    const updated = [newSearch, ...recentSearches.filter(s => s.query !== query)].slice(0, 10);
    setRecentSearches(updated);
    localStorage.setItem('searchHistory', JSON.stringify(updated));
  };

  // Effectuer la recherche
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    
    try {
      // Simuler une recherche (à remplacer par un vrai appel API)
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'student',
          title: 'Jean Dupont',
          subtitle: 'Étudiant - CE2',
          description: 'Présent aujourd\'hui, arrivé à 08:15',
          url: '/students/1',
          icon: <UserIcon className="h-5 w-5" />,
          metadata: { status: 'present', date: '2025-01-31' }
        },
        {
          id: '2',
          type: 'class',
          title: 'CE2',
          subtitle: 'Classe',
          description: '28 étudiants, 85% de présence',
          url: '/classes/ce2',
          icon: <AcademicCapIcon className="h-5 w-5" />,
          metadata: { count: 28 }
        },
        {
          id: '3',
          type: 'presence',
          title: 'Présences du 31/01/2025',
          subtitle: 'Rapport de présence',
          description: '214 présents, 23 absents, 8 en retard',
          url: '/presences/2025-01-31',
          icon: <ChartBarIcon className="h-5 w-5" />,
          metadata: { date: '2025-01-31' }
        }
      ].filter(result => 
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.subtitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );

      setTimeout(() => {
        setResults(mockResults);
        setIsLoading(false);
        if (mockResults.length > 0) {
          saveSearchHistory(searchQuery, mockResults.length);
        }
      }, 300);

    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
      setIsLoading(false);
    }
  };

  // Debounce pour la recherche
  useEffect(() => {
    const timer = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Gestion des raccourcis clavier
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + K pour ouvrir la recherche
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
        setTimeout(() => inputRef.current?.focus(), 100);
      }

      // Escape pour fermer
      if (e.key === 'Escape') {
        setIsOpen(false);
        setQuery('');
        setResults([]);
        setSelectedIndex(-1);
      }

      // Navigation avec les flèches
      if (isOpen && results.length > 0) {
        if (e.key === 'ArrowDown') {
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % results.length);
        } else if (e.key === 'ArrowUp') {
          e.preventDefault();
          setSelectedIndex(prev => prev <= 0 ? results.length - 1 : prev - 1);
        } else if (e.key === 'Enter' && selectedIndex >= 0) {
          e.preventDefault();
          handleResultClick(results[selectedIndex]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex]);

  // Fermer lors du clic à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleResultClick = (result: SearchResult) => {
    navigate(result.url);
    setIsOpen(false);
    setQuery('');
    setResults([]);
    setSelectedIndex(-1);
  };

  const handleRecentSearchClick = (search: SearchHistory) => {
    setQuery(search.query);
    performSearch(search.query);
  };

  const clearHistory = () => {
    setRecentSearches([]);
    localStorage.removeItem('searchHistory');
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'student':
        return <UserIcon className="h-4 w-4 text-blue-500" />;
      case 'class':
        return <AcademicCapIcon className="h-4 w-4 text-green-500" />;
      case 'presence':
        return <ChartBarIcon className="h-4 w-4 text-purple-500" />;
      default:
        return <MagnifyingGlassIcon className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <>
      {/* Bouton de recherche */}
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
      >
        <MagnifyingGlassIcon className="h-4 w-4" />
        <span>Rechercher...</span>
        <kbd className="hidden sm:inline-flex items-center px-2 py-1 text-xs font-mono bg-gray-200 dark:bg-gray-700 rounded">
          ⌘K
        </kbd>
      </button>

      {/* Modal de recherche */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-start justify-center p-4 pt-16">
            <div className="fixed inset-0 bg-black bg-opacity-25 transition-opacity" />
            
            <div
              ref={searchRef}
              className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-xl shadow-2xl"
            >
              {/* Barre de recherche */}
              <div className="flex items-center px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 mr-3" />
                <input
                  ref={inputRef}
                  type="text"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Rechercher des étudiants, classes, présences..."
                  className="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  autoFocus
                />
                {query && (
                  <button
                    onClick={() => {
                      setQuery('');
                      setResults([]);
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                  >
                    <XMarkIcon className="h-4 w-4" />
                  </button>
                )}
              </div>

              {/* Contenu */}
              <div className="max-h-96 overflow-y-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-500 dark:text-gray-400">Recherche...</span>
                  </div>
                ) : query && results.length > 0 ? (
                  <div className="py-2">
                    <div className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Résultats ({results.length})
                    </div>
                    {results.map((result, index) => (
                      <button
                        key={result.id}
                        onClick={() => handleResultClick(result)}
                        className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          index === selectedIndex ? 'bg-gray-50 dark:bg-gray-700' : ''
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getTypeIcon(result.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {result.title}
                              </p>
                              {result.metadata?.status && (
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                  result.metadata.status === 'present' 
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                                }`}>
                                  {result.metadata.status === 'present' ? 'Présent' : 'Absent'}
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {result.subtitle}
                            </p>
                            {result.description && (
                              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                {result.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : query && !isLoading ? (
                  <div className="py-8 text-center">
                    <MagnifyingGlassIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">Aucun résultat trouvé</p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">
                      Essayez avec d'autres mots-clés
                    </p>
                  </div>
                ) : (
                  <div className="py-2">
                    {recentSearches.length > 0 && (
                      <>
                        <div className="flex items-center justify-between px-4 py-2">
                          <span className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Recherches récentes
                          </span>
                          <button
                            onClick={clearHistory}
                            className="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            Effacer
                          </button>
                        </div>
                        {recentSearches.slice(0, 5).map((search, index) => (
                          <button
                            key={index}
                            onClick={() => handleRecentSearchClick(search)}
                            className="w-full px-4 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            <div className="flex items-center space-x-3">
                              <ClockIcon className="h-4 w-4 text-gray-400" />
                              <div className="flex-1">
                                <p className="text-sm text-gray-900 dark:text-white">
                                  {search.query}
                                </p>
                                <p className="text-xs text-gray-400 dark:text-gray-500">
                                  {search.results} résultat{search.results > 1 ? 's' : ''} • {search.timestamp.toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </button>
                        ))}
                      </>
                    )}
                    
                    <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 mt-2">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-700 rounded mr-1">↑↓</kbd>
                        pour naviguer
                        <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-700 rounded mx-1">↵</kbd>
                        pour sélectionner
                        <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-700 rounded ml-1">esc</kbd>
                        pour fermer
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default GlobalSearch;

<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Fond -->
  <rect width="192" height="192" rx="20" fill="url(#grad1)"/>
  
  <!-- Cercle central -->
  <circle cx="96" cy="96" r="60" fill="white"/>
  
  <!-- Texte GPI -->
  <text x="96" y="106" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="#667eea">GPI</text>
  
  <!-- <PERSON><PERSON><PERSON> de présence (petits points) -->
  <circle cx="76" cy="76" r="3" fill="#667eea" opacity="0.6"/>
  <circle cx="96" cy="76" r="3" fill="#667eea" opacity="0.6"/>
  <circle cx="116" cy="76" r="3" fill="#667eea" opacity="0.6"/>
  
  <!-- Icône de reconnaissance (œil stylisé) -->
  <ellipse cx="96" cy="126" rx="12" ry="6" fill="#667eea" opacity="0.6"/>
  <circle cx="96" cy="126" r="3" fill="white"/>
</svg>

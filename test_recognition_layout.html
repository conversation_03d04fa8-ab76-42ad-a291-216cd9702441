<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Reconnaissance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .camera-frame {
            background: black;
            border-radius: 8px;
            height: 280px;
            margin: 24px 24px 16px 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        .mode-selector {
            display: flex;
            justify-content: center;
            padding: 12px 24px;
        }
        .mode-buttons {
            display: inline-flex;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .mode-btn {
            padding: 8px 16px;
            font-size: 14px;
            border: 1px solid #d1d5db;
            background: white;
            cursor: pointer;
        }
        .mode-btn.active {
            background: #2563eb;
            color: white;
        }
        .mode-btn:first-child {
            border-radius: 6px 0 0 6px;
        }
        .mode-btn:last-child {
            border-radius: 0 6px 6px 0;
        }
        .camera-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 24px 16px 24px;
        }
        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 16px;
            margin-bottom: 12px;
        }
        .control-btn {
            padding: 8px 16px;
            font-size: 14px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .start-btn {
            background: #2563eb;
            color: white;
        }
        .stop-btn {
            background: #dc2626;
            color: white;
        }
        .speech-toggle {
            display: flex;
            align-items: center;
            margin-top: 4px;
            font-size: 14px;
        }
        .toggle-switch {
            width: 44px;
            height: 24px;
            background: #d1d5db;
            border-radius: 12px;
            position: relative;
            margin-right: 12px;
            cursor: pointer;
        }
        .toggle-switch.active {
            background: #2563eb;
        }
        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.2s;
        }
        .toggle-switch.active::after {
            transform: translateX(20px);
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .viewport-info {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #0d47a1;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Test Layout Page Reconnaissance</h1>
        <p>Simulation de la nouvelle mise en page optimisée pour la page de reconnaissance faciale.</p>
        
        <div class="viewport-info">
            <h3>📏 Informations de la fenêtre</h3>
            <p><strong>Hauteur de la fenêtre:</strong> <span id="viewport-height">-</span>px</p>
            <p><strong>Hauteur du contenu:</strong> <span id="content-height">-</span>px</p>
            <p><strong>Défilement nécessaire:</strong> <span id="scroll-needed">-</span></p>
        </div>

        <div class="success">
            <h3>✅ Optimisations appliquées</h3>
            <ul>
                <li><strong>Cadre caméra:</strong> Hauteur fixe de 280px (au lieu de aspect-video)</li>
                <li><strong>Espacement réduit:</strong> Marges et paddings optimisés</li>
                <li><strong>Boutons plus compacts:</strong> Taille réduite (px-4 py-2 au lieu de px-6 py-3)</li>
                <li><strong>Texte plus petit:</strong> text-sm pour les boutons</li>
                <li><strong>Icônes réduites:</strong> size={16} au lieu de size={18}</li>
            </ul>
        </div>

        <h2>🎬 Simulation du cadre de reconnaissance</h2>
        
        <!-- Simulation du cadre de reconnaissance -->
        <div style="background: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border: 1px solid #e5e7eb;">
            <!-- Header -->
            <div style="display: flex; align-items: center; justify-content: between; padding: 24px; border-bottom: 1px solid #e5e7eb;">
                <h2 style="margin: 0; font-size: 20px; font-weight: bold;">📷 Reconnaissance Faciale</h2>
                <span style="background: #dbeafe; color: #1d4ed8; padding: 4px 12px; border-radius: 9999px; font-size: 12px;">Caméra inactive</span>
            </div>

            <!-- Cadre caméra optimisé -->
            <div class="camera-frame">
                📹 Cadre caméra (280px de hauteur)
            </div>

            <!-- Sélecteur de mode -->
            <div class="mode-selector">
                <div class="mode-buttons">
                    <button class="mode-btn active">🚪 Arrivée</button>
                    <button class="mode-btn">🚶 Départ</button>
                </div>
            </div>

            <!-- Contrôles caméra -->
            <div class="camera-controls">
                <div class="control-buttons">
                    <button class="control-btn start-btn">
                        ▶️ Démarrer la caméra
                    </button>
                </div>

                <!-- Toggle synthèse vocale -->
                <div class="speech-toggle">
                    <div class="toggle-switch active"></div>
                    <span>Synthèse vocale</span>
                </div>
            </div>
        </div>

        <div class="success" style="margin-top: 20px;">
            <h3>🎯 Résultat</h3>
            <p>Avec ces optimisations, tous les éléments de contrôle (boutons Arrivée/Départ, Démarrer la caméra, et Synthèse vocale) sont maintenant visibles sans défilement sur la plupart des écrans.</p>
        </div>
    </div>

    <script>
        function updateViewportInfo() {
            const viewportHeight = window.innerHeight;
            const contentHeight = document.body.scrollHeight;
            const scrollNeeded = contentHeight > viewportHeight;

            document.getElementById('viewport-height').textContent = viewportHeight;
            document.getElementById('content-height').textContent = contentHeight;
            document.getElementById('scroll-needed').textContent = scrollNeeded ? 'Oui' : 'Non';
            document.getElementById('scroll-needed').style.color = scrollNeeded ? '#dc2626' : '#059669';
        }

        // Mettre à jour les informations au chargement et lors du redimensionnement
        updateViewportInfo();
        window.addEventListener('resize', updateViewportInfo);

        // Simuler l'interaction avec les boutons
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.mode-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelector('.toggle-switch').addEventListener('click', function() {
            this.classList.toggle('active');
        });
    </script>
</body>
</html>

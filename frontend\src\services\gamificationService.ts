import { api } from './apiService';

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earned: boolean;
  earnedDate?: Date;
  progress?: number;
  maxProgress?: number;
  category: 'attendance' | 'punctuality' | 'streak' | 'achievement';
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  points: number;
  type: 'attendance' | 'punctuality' | 'streak' | 'social';
  completed: boolean;
  progress: number;
  maxProgress: number;
  reward?: string;
  deadline?: Date;
}

export interface StudentStats {
  studentId: number;
  totalPoints: number;
  level: number;
  currentStreak: number;
  longestStreak: number;
  attendanceRate: number;
  rank: number;
  totalStudents: number;
  badges: Badge[];
  achievements: Achievement[];
  weeklyProgress: number[];
  monthlyPoints: number[];
}

export interface LeaderboardEntry {
  rank: number;
  studentId: string;
  name: string;
  points: number;
  level: number;
  avatar?: string;
  streak: number;
  isCurrentUser?: boolean;
  change: number; // Changement de position depuis la semaine dernière
}

export interface GamificationConfig {
  pointsPerAttendance: number;
  pointsPerPunctuality: number;
  streakMultiplier: number;
  levelThresholds: number[];
  badgeRequirements: Record<string, any>;
}

class GamificationService {
  /**
   * Récupère les statistiques de gamification d'un étudiant
   */
  async getStudentStats(studentId: number): Promise<StudentStats> {
    try {
      const response = await api.get(`/gamification/students/${studentId}/stats/`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats:', error);
      return this.getMockStudentStats();
    }
  }

  /**
   * Récupère le classement général
   */
  async getLeaderboard(
    period: 'week' | 'month' | 'year' = 'week',
    classId?: number,
    limit: number = 50
  ): Promise<LeaderboardEntry[]> {
    try {
      const params = new URLSearchParams();
      params.append('period', period);
      params.append('limit', limit.toString());
      if (classId) params.append('class_id', classId.toString());

      const response = await api.get(`/gamification/leaderboard/?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération du classement:', error);
      return this.getMockLeaderboard();
    }
  }

  /**
   * Récupère tous les badges disponibles
   */
  async getAllBadges(): Promise<Badge[]> {
    try {
      const response = await api.get('/gamification/badges/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des badges:', error);
      return [];
    }
  }

  /**
   * Récupère tous les achievements disponibles
   */
  async getAllAchievements(): Promise<Achievement[]> {
    try {
      const response = await api.get('/gamification/achievements/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des achievements:', error);
      return [];
    }
  }

  /**
   * Attribue des points à un étudiant
   */
  async awardPoints(
    studentId: number,
    points: number,
    reason: string,
    category: 'attendance' | 'punctuality' | 'bonus' = 'attendance'
  ): Promise<{ success: boolean; newTotal: number; levelUp?: boolean }> {
    try {
      const response = await api.post(`/gamification/students/${studentId}/award-points/`, {
        points,
        reason,
        category
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'attribution des points:', error);
      return { success: false, newTotal: 0 };
    }
  }

  /**
   * Décerne un badge à un étudiant
   */
  async awardBadge(studentId: number, badgeId: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.post(`/gamification/students/${studentId}/award-badge/`, {
        badge_id: badgeId
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'attribution du badge:', error);
      return { success: false, message: 'Erreur lors de l\'attribution du badge' };
    }
  }

  /**
   * Met à jour la progression d'un achievement
   */
  async updateAchievementProgress(
    studentId: number,
    achievementId: string,
    progress: number
  ): Promise<{ success: boolean; completed?: boolean }> {
    try {
      const response = await api.patch(`/gamification/students/${studentId}/achievements/${achievementId}/`, {
        progress
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la mise à jour de l\'achievement:', error);
      return { success: false };
    }
  }

  /**
   * Récupère l'historique des points d'un étudiant
   */
  async getPointsHistory(
    studentId: number,
    startDate?: string,
    endDate?: string
  ): Promise<Array<{
    date: string;
    points: number;
    reason: string;
    category: string;
  }>> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('start_date', startDate);
      if (endDate) params.append('end_date', endDate);

      const response = await api.get(`/gamification/students/${studentId}/points-history/?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  }

  /**
   * Calcule le niveau basé sur les points
   */
  calculateLevel(points: number, thresholds: number[] = [0, 100, 250, 500, 1000, 2000, 3500, 5500, 8000, 12000]): number {
    for (let i = thresholds.length - 1; i >= 0; i--) {
      if (points >= thresholds[i]) {
        return i + 1;
      }
    }
    return 1;
  }

  /**
   * Calcule les points nécessaires pour le niveau suivant
   */
  getPointsToNextLevel(currentPoints: number, thresholds: number[] = [0, 100, 250, 500, 1000, 2000, 3500, 5500, 8000, 12000]): number {
    const currentLevel = this.calculateLevel(currentPoints, thresholds);
    if (currentLevel >= thresholds.length) return 0; // Niveau max atteint
    
    return thresholds[currentLevel] - currentPoints;
  }

  /**
   * Vérifie si un étudiant mérite un nouveau badge
   */
  async checkBadgeEligibility(studentId: number): Promise<Badge[]> {
    try {
      const response = await api.get(`/gamification/students/${studentId}/check-badges/`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la vérification des badges:', error);
      return [];
    }
  }

  /**
   * Récupère les statistiques globales de gamification
   */
  async getGlobalStats(): Promise<{
    totalPointsAwarded: number;
    totalBadgesEarned: number;
    averageLevel: number;
    topPerformers: LeaderboardEntry[];
    recentAchievements: Array<{
      studentName: string;
      achievementName: string;
      date: string;
    }>;
  }> {
    try {
      const response = await api.get('/gamification/global-stats/');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des stats globales:', error);
      return {
        totalPointsAwarded: 0,
        totalBadgesEarned: 0,
        averageLevel: 1,
        topPerformers: [],
        recentAchievements: []
      };
    }
  }

  /**
   * Données mockées pour les tests
   */
  private getMockStudentStats(): StudentStats {
    return {
      studentId: 1,
      totalPoints: 1250,
      level: 8,
      currentStreak: 12,
      longestStreak: 25,
      attendanceRate: 94.5,
      rank: 3,
      totalStudents: 245,
      weeklyProgress: [85, 90, 88, 95, 92, 89, 94],
      monthlyPoints: [200, 180, 220, 250, 180, 220],
      badges: [
        {
          id: '1',
          name: 'Ponctuel',
          description: 'Arrivé à l\'heure 10 fois de suite',
          icon: '⏰',
          rarity: 'common',
          earned: true,
          earnedDate: new Date('2025-01-15'),
          category: 'punctuality'
        },
        {
          id: '2',
          name: 'Assidu',
          description: 'Présent 30 jours consécutifs',
          icon: '📚',
          rarity: 'rare',
          earned: true,
          earnedDate: new Date('2025-01-20'),
          category: 'streak'
        }
      ],
      achievements: [
        {
          id: '1',
          title: 'Première semaine parfaite',
          description: 'Être présent tous les jours de la semaine',
          points: 100,
          type: 'attendance',
          completed: true,
          progress: 5,
          maxProgress: 5,
          reward: 'Badge Ponctuel'
        }
      ]
    };
  }

  /**
   * Classement mocké pour les tests
   */
  private getMockLeaderboard(): LeaderboardEntry[] {
    return [
      { rank: 1, studentId: '1', name: 'Marie Dubois', points: 1580, level: 10, streak: 28, change: 0 },
      { rank: 2, studentId: '2', name: 'Pierre Martin', points: 1420, level: 9, streak: 15, change: 1 },
      { rank: 3, studentId: '3', name: 'Sophie Durand', points: 1250, level: 8, streak: 12, isCurrentUser: true, change: -1 },
      { rank: 4, studentId: '4', name: 'Lucas Bernard', points: 1180, level: 8, streak: 8, change: 2 },
      { rank: 5, studentId: '5', name: 'Emma Petit', points: 1050, level: 7, streak: 22, change: -1 }
    ];
  }

  /**
   * Formate les données pour l'affichage
   */
  formatBadgeRarity(rarity: string): { color: string; label: string } {
    switch (rarity) {
      case 'common':
        return { color: 'text-gray-600 bg-gray-100', label: 'Commun' };
      case 'rare':
        return { color: 'text-blue-600 bg-blue-100', label: 'Rare' };
      case 'epic':
        return { color: 'text-purple-600 bg-purple-100', label: 'Épique' };
      case 'legendary':
        return { color: 'text-yellow-600 bg-yellow-100', label: 'Légendaire' };
      default:
        return { color: 'text-gray-600 bg-gray-100', label: 'Commun' };
    }
  }

  /**
   * Génère des suggestions pour améliorer le score
   */
  generateImprovementSuggestions(stats: StudentStats): string[] {
    const suggestions: string[] = [];

    if (stats.attendanceRate < 90) {
      suggestions.push('Améliorer votre taux de présence pour gagner plus de points');
    }

    if (stats.currentStreak < 7) {
      suggestions.push('Maintenir une série de présence pour débloquer des bonus');
    }

    if (stats.badges.filter(b => b.earned).length < 3) {
      suggestions.push('Débloquer plus de badges pour augmenter votre niveau');
    }

    return suggestions;
  }
}

export const gamificationService = new GamificationService();

import React, { useState, useEffect } from 'react';
import { TrophyIcon, StarIcon, FireIcon, CalendarIcon, UserGroupIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { TrophyIcon as TrophyIconSolid, StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  earned: boolean;
  earnedDate?: Date;
  progress?: number;
  maxProgress?: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  points: number;
  type: 'attendance' | 'punctuality' | 'streak' | 'social';
  completed: boolean;
  progress: number;
  maxProgress: number;
  reward?: string;
}

interface StudentStats {
  totalPoints: number;
  level: number;
  currentStreak: number;
  longestStreak: number;
  attendanceRate: number;
  rank: number;
  totalStudents: number;
  badges: Badge[];
  achievements: Achievement[];
  weeklyProgress: number[];
}

interface LeaderboardEntry {
  rank: number;
  studentId: string;
  name: string;
  points: number;
  level: number;
  avatar?: string;
  streak: number;
  isCurrentUser?: boolean;
}

const GamificationDashboard: React.FC<{ studentId?: string }> = ({ studentId }) => {
  const [stats, setStats] = useState<StudentStats | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'badges' | 'achievements' | 'leaderboard'>('overview');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadGamificationData();
  }, [studentId]);

  const loadGamificationData = async () => {
    setIsLoading(true);
    try {
      // Simuler des données de gamification
      const mockStats: StudentStats = {
        totalPoints: 1250,
        level: 8,
        currentStreak: 12,
        longestStreak: 25,
        attendanceRate: 94.5,
        rank: 3,
        totalStudents: 245,
        weeklyProgress: [85, 90, 88, 95, 92, 89, 94],
        badges: [
          {
            id: '1',
            name: 'Ponctuel',
            description: 'Arrivé à l\'heure 10 fois de suite',
            icon: '⏰',
            rarity: 'common',
            earned: true,
            earnedDate: new Date('2025-01-15')
          },
          {
            id: '2',
            name: 'Assidu',
            description: 'Présent 30 jours consécutifs',
            icon: '📚',
            rarity: 'rare',
            earned: true,
            earnedDate: new Date('2025-01-20')
          },
          {
            id: '3',
            name: 'Champion',
            description: 'Top 3 du classement mensuel',
            icon: '🏆',
            rarity: 'epic',
            earned: true,
            earnedDate: new Date('2025-01-25')
          },
          {
            id: '4',
            name: 'Légende',
            description: 'Présent tous les jours du trimestre',
            icon: '👑',
            rarity: 'legendary',
            earned: false,
            progress: 45,
            maxProgress: 60
          }
        ],
        achievements: [
          {
            id: '1',
            title: 'Première semaine parfaite',
            description: 'Être présent tous les jours de la semaine',
            points: 100,
            type: 'attendance',
            completed: true,
            progress: 5,
            maxProgress: 5,
            reward: 'Badge Ponctuel'
          },
          {
            id: '2',
            title: 'Série de 20',
            description: 'Maintenir une série de 20 jours de présence',
            points: 250,
            type: 'streak',
            completed: false,
            progress: 12,
            maxProgress: 20,
            reward: 'Badge Assidu'
          },
          {
            id: '3',
            title: 'Jamais en retard',
            description: 'Arriver à l\'heure pendant un mois entier',
            points: 200,
            type: 'punctuality',
            completed: false,
            progress: 18,
            maxProgress: 30,
            reward: '50 points bonus'
          }
        ]
      };

      const mockLeaderboard: LeaderboardEntry[] = [
        { rank: 1, studentId: '1', name: 'Marie Dubois', points: 1580, level: 10, streak: 28 },
        { rank: 2, studentId: '2', name: 'Pierre Martin', points: 1420, level: 9, streak: 15 },
        { rank: 3, studentId: '3', name: 'Sophie Durand', points: 1250, level: 8, streak: 12, isCurrentUser: true },
        { rank: 4, studentId: '4', name: 'Lucas Bernard', points: 1180, level: 8, streak: 8 },
        { rank: 5, studentId: '5', name: 'Emma Petit', points: 1050, level: 7, streak: 22 }
      ];

      setTimeout(() => {
        setStats(mockStats);
        setLeaderboard(mockLeaderboard);
        setIsLoading(false);
      }, 1000);

    } catch (error) {
      console.error('Erreur lors du chargement des données de gamification:', error);
      setIsLoading(false);
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
      case 'rare': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'epic': return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';
      case 'legendary': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getProgressColor = (progress: number, max: number) => {
    const percentage = (progress / max) * 100;
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-yellow-500';
    if (percentage >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">Impossible de charger les données de gamification</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête avec statistiques principales */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold">Niveau {stats.level}</h2>
            <p className="text-blue-100">Rang #{stats.rank} sur {stats.totalStudents}</p>
          </div>
          <div className="text-right">
            <p className="text-3xl font-bold">{stats.totalPoints}</p>
            <p className="text-blue-100">points</p>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <FireIcon className="h-6 w-6 text-orange-300 mr-1" />
              <span className="text-2xl font-bold">{stats.currentStreak}</span>
            </div>
            <p className="text-sm text-blue-100">Série actuelle</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <CalendarIcon className="h-6 w-6 text-green-300 mr-1" />
              <span className="text-2xl font-bold">{stats.attendanceRate}%</span>
            </div>
            <p className="text-sm text-blue-100">Présence</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <TrophyIconSolid className="h-6 w-6 text-yellow-300 mr-1" />
              <span className="text-2xl font-bold">{stats.badges.filter(b => b.earned).length}</span>
            </div>
            <p className="text-sm text-blue-100">Badges</p>
          </div>
        </div>
      </div>

      {/* Navigation par onglets */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Vue d\'ensemble', icon: ChartBarIcon },
            { id: 'badges', name: 'Badges', icon: StarIcon },
            { id: 'achievements', name: 'Défis', icon: TrophyIcon },
            { id: 'leaderboard', name: 'Classement', icon: UserGroupIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as any)}
              className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                selectedTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      {selectedTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Progression hebdomadaire */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Progression cette semaine
            </h3>
            <div className="space-y-3">
              {['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'].map((day, index) => (
                <div key={day} className="flex items-center">
                  <span className="w-8 text-sm text-gray-600 dark:text-gray-400">{day}</span>
                  <div className="flex-1 mx-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getProgressColor(stats.weeklyProgress[index], 100)}`}
                      style={{ width: `${stats.weeklyProgress[index]}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {stats.weeklyProgress[index]}%
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Badges récents */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Badges récents
            </h3>
            <div className="space-y-3">
              {stats.badges.filter(b => b.earned).slice(0, 3).map((badge) => (
                <div key={badge.id} className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${getRarityColor(badge.rarity)}`}>
                    {badge.icon}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 dark:text-white">{badge.name}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{badge.description}</p>
                  </div>
                  {badge.earnedDate && (
                    <span className="text-xs text-gray-400">
                      {badge.earnedDate.toLocaleDateString()}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'badges' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {stats.badges.map((badge) => (
            <div
              key={badge.id}
              className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${
                badge.earned ? 'ring-2 ring-blue-500' : 'opacity-75'
              }`}
            >
              <div className="text-center">
                <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center text-2xl mb-4 ${getRarityColor(badge.rarity)}`}>
                  {badge.icon}
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{badge.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{badge.description}</p>
                
                {badge.earned ? (
                  <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                    <StarIconSolid className="h-5 w-5 mr-1" />
                    <span className="text-sm font-medium">Obtenu</span>
                  </div>
                ) : badge.progress !== undefined && badge.maxProgress !== undefined ? (
                  <div>
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                      <span>Progression</span>
                      <span>{badge.progress}/{badge.maxProgress}</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(badge.progress / badge.maxProgress) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ) : (
                  <span className="text-sm text-gray-500 dark:text-gray-400">Non obtenu</span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {selectedTab === 'achievements' && (
        <div className="space-y-4">
          {stats.achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`bg-white dark:bg-gray-800 rounded-lg shadow p-6 ${
                achievement.completed ? 'ring-2 ring-green-500' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h3 className="font-semibold text-gray-900 dark:text-white mr-2">
                      {achievement.title}
                    </h3>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                      {achievement.points} pts
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 mb-3">{achievement.description}</p>
                  
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Progression: {achievement.progress}/{achievement.maxProgress}
                    </span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {Math.round((achievement.progress / achievement.maxProgress) * 100)}%
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
                    <div
                      className={`h-2 rounded-full ${
                        achievement.completed ? 'bg-green-500' : 'bg-blue-600'
                      }`}
                      style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                    ></div>
                  </div>
                  
                  {achievement.reward && (
                    <p className="text-sm text-purple-600 dark:text-purple-400">
                      🎁 Récompense: {achievement.reward}
                    </p>
                  )}
                </div>
                
                {achievement.completed && (
                  <TrophyIconSolid className="h-8 w-8 text-yellow-500 ml-4" />
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {selectedTab === 'leaderboard' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Classement de la semaine
            </h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {leaderboard.map((entry) => (
              <div
                key={entry.studentId}
                className={`px-6 py-4 flex items-center justify-between ${
                  entry.isCurrentUser ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                    entry.rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                    entry.rank === 2 ? 'bg-gray-100 text-gray-800' :
                    entry.rank === 3 ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-50 text-gray-600'
                  }`}>
                    {entry.rank}
                  </div>
                  <div>
                    <p className={`font-medium ${
                      entry.isCurrentUser ? 'text-blue-900 dark:text-blue-100' : 'text-gray-900 dark:text-white'
                    }`}>
                      {entry.name} {entry.isCurrentUser && '(Vous)'}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Niveau {entry.level} • Série de {entry.streak} jours
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {entry.points} pts
                  </p>
                  {entry.rank <= 3 && (
                    <TrophyIconSolid className={`h-5 w-5 ${
                      entry.rank === 1 ? 'text-yellow-500' :
                      entry.rank === 2 ? 'text-gray-400' :
                      'text-orange-500'
                    }`} />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default GamificationDashboard;
